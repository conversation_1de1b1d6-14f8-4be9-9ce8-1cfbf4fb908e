# Thermal Downscaling of Land Surface Temperature Using Weighted Spectral Indices Integration: A Cluster-Based Approach

## 3. Methodology

### 3.1 Cluster-Based Analysis Approach

To address the challenge of cloud coverage in satellite imagery over Berlin, a cluster-based analysis approach was adopted. Rather than attempting to analyze the entire city, which would be compromised by cloud-affected pixels, four distinct non-intersecting clusters were selected within the city. Each cluster was carefully chosen to:

1. Have zero cloud coverage during the study period
2. Represent a different urban context with distinct land cover characteristics
3. Be of sufficient size for meaningful statistical analysis
4. Not intersect with other clusters to ensure independent analysis

This approach offers several methodological advantages:
- Eliminates the influence of cloud-affected pixels on the analysis
- Enables comparison of downscaling performance across different urban contexts
- Provides more reliable and consistent results for each area
- Allows for the assessment of how different urban morphologies affect the relationship between spectral indices and LST

### 3.2 Cluster Selection and Characteristics

Four rectangular clusters of equal dimensions were defined within Berlin, specifically selected to avoid areas with cloud coverage:

1. **City Center Cluster (Alexanderplatz)**: Located in the dense urban core of Berlin around Alexanderplatz, this cluster is characterized by high building density, minimal vegetation, extensive impervious surfaces, and significant anthropogenic heat sources. Coordinates: 13.41°E-13.45°E, 52.52°N-52.55°N.

2. **Mixed Urban Cluster (Prenzlauer Berg)**: This cluster represents an urban residential area with medium density development, street trees, and small urban parks. It provides a good representation of typical inner-city neighborhoods. Coordinates: 13.42°E-13.46°E, 52.56°N-52.59°N.

3. **Residential Cluster (Steglitz)**: Characterized by moderate-density residential development with private gardens, street trees, and small parks, this cluster represents typical urban residential areas with more vegetation than inner-city neighborhoods. Coordinates: 13.30°E-13.34°E, 52.41°N-52.44°N.

4. **Urban-Rural Transition Cluster (Köpenick)**: Located in the eastern part of Berlin, this cluster captures the transition zone between urban and rural environments, with lower building density and more extensive natural vegetation. Coordinates: 13.58°E-13.62°E, 52.41°N-52.44°N.

The clusters were deliberately positioned to avoid the Tiergarten area and other locations where cloud coverage was present during the study period, ensuring that all analysis was conducted on cloud-free pixels.

### 3.3 Data Sources

#### 3.3.1 Landsat 8 Collection 2 Level 2 (L8 C2 L2)

Landsat 8 Collection 2 Level 2 Surface Temperature data was used as the source of thermal information. The dataset specifications include:
- Spatial resolution: 100m (resampled from native 100m)
- Temporal resolution: 16 days
- Acquisition period: August 9-13, 2022
- Cloud cover threshold: <10% (for initial filtering, with selected clusters having 0% cloud cover)
- Processing level: Surface reflectance and surface temperature
- Key bands: ST_B10 (Surface Temperature)

#### 3.3.2 Sentinel-2 MSI Level 2A

Sentinel-2 data was used to derive high-resolution spectral indices:
- Spatial resolution: 10m (bands 2, 3, 4, 8), 20m (bands 11, 12)
- Temporal resolution: 5 days (combined Sentinel-2A and 2B)
- Acquisition period: August 9-13, 2022
- Cloud cover threshold: <10% (for initial filtering, with selected clusters having 0% cloud cover)
- Key bands:
  - B2 (Blue, 490nm): 10m
  - B3 (Green, 560nm): 10m
  - B4 (Red, 665nm): 10m
  - B8 (NIR, 842nm): 10m
  - B11 (SWIR, 1610nm): 20m

### 3.4 Data Preprocessing

#### 3.4.1 Cloud Masking

Although the selected clusters were chosen to have zero cloud coverage, cloud masking was still implemented in the processing chain to ensure data quality and consistency across the entire workflow:

For Sentinel-2, the QA60 band was used to identify and mask clouds and cirrus:
```javascript
var maskS2clouds = function(image) {
  var qa = image.select('QA60');
  var cloudBitMask = 1 << 10;
  var cirrusBitMask = 1 << 11;
  var mask = qa.bitwiseAnd(cloudBitMask).eq(0)
      .and(qa.bitwiseAnd(cirrusBitMask).eq(0));
  return image.updateMask(mask);
};
```

For Landsat 8, the QA_PIXEL band was used to identify and mask clouds:
```javascript
var cloudMask = qa.bitwiseAnd(1 << 3).eq(0)
                  .and(qa.bitwiseAnd(1 << 4).eq(0));
```

#### 3.4.2 LST Conversion

Landsat 8 thermal data (ST_B10) was converted from raw digital numbers to degrees Celsius:
```javascript
var thermal = image.select('ST_B10')
                   .multiply(0.00341802)
                   .add(149.0)
                   .subtract(273.15)
                   .rename('LST');
```

#### 3.4.3 Spectral Indices Calculation

Three key spectral indices were calculated from Sentinel-2 data for each cluster:

1. **NDVI (Normalized Difference Vegetation Index)**:
   ```javascript
   var ndvi = image.normalizedDifference(['B8', 'B4']).rename('NDVI');
   ```
   NDVI = (NIR - Red) / (NIR + Red)

2. **NDBI (Normalized Difference Built-up Index)**:
   ```javascript
   var ndbi = image.normalizedDifference(['B11', 'B8']).rename('NDBI');
   ```
   NDBI = (SWIR - NIR) / (SWIR + NIR)

3. **NDWI (Normalized Difference Water Index)**:
   ```javascript
   var ndwi = image.normalizedDifference(['B3', 'B8']).rename('NDWI');
   ```
   NDWI = (Green - NIR) / (Green + NIR)

#### 3.4.4 Composite Creation

To minimize the impact of temporal variations and data gaps, median composites were created for both LST and spectral indices over the study period for each cluster:
```javascript
var ndviComposite = s2_processed.select('NDVI').median().clip(cluster);
var ndbiComposite = s2_processed.select('NDBI').median().clip(cluster);
var ndwiComposite = s2_processed.select('NDWI').median().clip(cluster);
var lstComposite = l8_processed.select('LST').median().clip(cluster);
```

### 3.5 Statistical Analysis and Weight Derivation

#### 3.5.1 Correlation Analysis

For each cluster, Pearson correlation coefficients were calculated between LST and each spectral index to quantify their relationships:
```javascript
var calculateStats = function(lst, index, name) {
  // Resample index to LST resolution for proper comparison
  var index_resampled = index.reproject({
    crs: lst.projection(),
    scale: 100
  });
  
  // Combine LST and index
  var combined = ee.Image.cat([lst, index_resampled]);
  
  // Calculate correlation
  var stats = combined.reduceRegion({
    reducer: ee.Reducer.pearsonsCorrelation(),
    geometry: cluster,
    scale: 100,
    maxPixels: 1e13
  });
  
  return ee.Dictionary({
    'Index': name,
    'Correlation': stats.get('correlation'),
    'LST_mean': lst_stats.get('LST_mean'),
    'LST_stdDev': lst_stats.get('LST_stdDev'),
    'Index_mean': index_stats.get(name + '_mean'),
    'Index_stdDev': index_stats.get(name + '_stdDev')
  });
};
```

This analysis was performed independently for each cluster to determine how the relationships between spectral indices and LST vary across different urban contexts.

#### 3.5.2 Derivation of Empirical Weights

For each cluster, empirical weights for each index were derived based on their correlation with LST:
```javascript
var ndvi_corr = ee.Number(ndvi_stats.get('Correlation'));
var ndbi_corr = ee.Number(ndbi_stats.get('Correlation'));
var ndwi_corr = ee.Number(ndwi_stats.get('Correlation'));

var total_abs = ndvi_corr.abs()
  .add(ndbi_corr.abs())
  .add(ndwi_corr.abs());

var weights = {
  ndvi: ndvi_corr.divide(total_abs),
  ndbi: ndbi_corr.divide(total_abs),
  ndwi: ndwi_corr.divide(total_abs)
};
```

The weight for each index was calculated as the absolute value of its correlation coefficient divided by the sum of the absolute values of all correlation coefficients. This approach ensures that:
1. The weights sum to 1
2. Indices with stronger correlation (positive or negative) have greater influence
3. The sign of the correlation is preserved, allowing for proper directional influence

By calculating cluster-specific weights, the methodology accounts for the varying importance of different indices across urban contexts.

### 3.6 TLC Downscaling Implementation

The Temperature-Land Cover (TLC) downscaling algorithm was implemented for each cluster with the following steps:

#### 3.6.1 Algorithm Formulation

The TLC approach is based on the assumption that LST variations at fine resolution can be predicted from the relationship between LST and land cover characteristics at coarse resolution. The algorithm can be expressed as:

LST_fine = LST_coarse_mean + (LST_coarse_stddev / Index_coarse_stddev) × (Index_fine - Index_coarse_mean)

Where:
- LST_fine is the downscaled LST at fine resolution
- LST_coarse_mean is the mean LST within a local window
- LST_coarse_stddev is the standard deviation of LST within a local window
- Index_coarse_stddev is the standard deviation of the index within a local window
- Index_fine is the index value at fine resolution
- Index_coarse_mean is the mean index value within a local window

#### 3.6.2 Multi-Index Implementation

Our implementation extends the basic TLC approach by incorporating multiple indices with empirical weights:
```javascript
var tlcDownscale = function(lst, ndvi, ndbi, ndwi, weights) {
  // Resample LST to intermediate resolution (90m)
  var lst_coarse = lst.reproject({
    crs: lst.projection(),
    scale: 90
  });
  
  // Keep indices at 10m resolution
  var ndvi_fine = ndvi.reproject({
    crs: ndvi.projection(),
    scale: 10
  });
  var ndbi_fine = ndbi.reproject({
    crs: ndbi.projection(),
    scale: 10
  });
  var ndwi_fine = ndwi.reproject({
    crs: ndwi.projection(),
    scale: 10
  });
  
  // Define kernel for local statistics
  var kernel = ee.Kernel.square({
    radius: 90,
    units: 'meters',
    normalize: true
  });
  
  // Calculate LST statistics
  var lst_mean = lst_coarse.reduceNeighborhood({
    reducer: ee.Reducer.mean(),
    kernel: kernel
  });
  
  var lst_stddev = lst_coarse.reduceNeighborhood({
    reducer: ee.Reducer.stdDev(),
    kernel: kernel
  });

  // Apply empirical weights to indices
  var combined_index = ndvi_fine.multiply(weights.ndvi)
    .add(ndbi_fine.multiply(weights.ndbi))
    .add(ndwi_fine.multiply(weights.ndwi))
    .rename('combined_index');
    
  // Calculate statistics for combined index
  var combined_mean = combined_index.reduceNeighborhood({
    reducer: ee.Reducer.mean(),
    kernel: kernel
  });
  
  var combined_stddev = combined_index.reduceNeighborhood({
    reducer: ee.Reducer.stdDev(),
    kernel: kernel
  });
  
  // Calculate scaling factor
  var scaling_factor = lst_stddev.divide(combined_stddev);
  
  // Calculate final downscaled LST
  var downscaled_lst = combined_index.multiply(scaling_factor)
    .add(lst_mean.subtract(combined_mean.multiply(scaling_factor)))
    .rename('LST_TLC');
    
  return downscaled_lst;
};
```

Key innovations in our implementation include:
1. **Weighted combination of multiple indices**: Instead of using a single index, we combine NDVI, NDBI, and NDWI with weights derived from their correlation with LST.
2. **Adaptive kernel size**: A square kernel with a radius of 90 meters is used to calculate local statistics, balancing local detail and statistical robustness.
3. **Intermediate resampling**: LST is resampled to an intermediate resolution (90m) before downscaling to 10m, reducing artifacts and preserving thermal patterns.

### 3.7 Validation Methodology

For each cluster, the downscaled LST product was validated against the original LST data to assess the accuracy of the downscaling process:
```javascript
var validateDownscaling = function(original, downscaled, region, name) {
  // Resample downscaled to original resolution for comparison
  var downscaled_resampled = downscaled.reproject({
    crs: original.projection(),
    scale: 100
  });
  
  var diff = downscaled_resampled.subtract(original);

  // Calculate RMSE
  var rmse = diff.pow(2).reduceRegion({
    reducer: ee.Reducer.mean(),
    geometry: region,
    scale: 100,
    maxPixels: 1e13
  }).get('LST_TLC');
  
  rmse = ee.Number(rmse).sqrt();
  
  // Calculate correlation between original and downscaled
  var combined = ee.Image.cat([original, downscaled_resampled]);
  var corr = combined.reduceRegion({
    reducer: ee.Reducer.pearsonsCorrelation(),
    geometry: region,
    scale: 100,
    maxPixels: 1e13
  }).get('correlation');
  
  // Calculate R-squared
  var originalMean = original.reduceRegion({
    reducer: ee.Reducer.mean(),
    geometry: region,
    scale: 100,
    maxPixels: 1e13
  }).get('LST');
  
  var totalSumSquares = original.subtract(ee.Image.constant(originalMean))
    .pow(2)
    .reduceRegion({
      reducer: ee.Reducer.sum(),
      geometry: region,
      scale: 100,
      maxPixels: 1e13
    }).get('LST');
  
  var residualSumSquares = diff.pow(2).reduceRegion({
    reducer: ee.Reducer.sum(),
    geometry: region,
    scale: 100,
    maxPixels: 1e13
  }).get('LST_TLC');
  
  var rSquared = ee.Number(1).subtract(ee.Number(residualSumSquares).divide(ee.Number(totalSumSquares)));
  
  return {
    'Method': name,
    'RMSE': rmse,
    'Correlation': corr,
    'R_squared': rSquared
  };
};
```

The validation approach includes:
1. **Resampling**: The downscaled LST (10m) is resampled back to the original resolution (100m) for direct comparison.
2. **Difference calculation**: The difference between the resampled downscaled LST and the original LST is calculated.
3. **Statistical metrics**: Several metrics are computed to quantify the agreement between the original and downscaled data:
   - Root Mean Square Error (RMSE): Square root of the average squared differences
   - Pearson Correlation Coefficient: Measures the linear relationship between original and downscaled LST
   - R-squared: Coefficient of determination, indicating the proportion of variance in the original LST that is predictable from the downscaled LST

### 3.8 Comparative Analysis

To evaluate the effectiveness of the multi-index weighted approach, a comparative analysis was conducted for each cluster against traditional single-index TLC downscaling methods. The same downscaling algorithm was applied using:
1. NDVI only (weights: NDVI=1, NDBI=0, NDWI=0)
2. NDBI only (weights: NDVI=0, NDBI=1, NDWI=0)
3. NDWI only (weights: NDVI=0, NDBI=0, NDWI=1)
4. The proposed weighted combination of all three indices

For each approach and each cluster, the same validation metrics (RMSE, correlation coefficient, R-squared) were calculated to enable direct comparison of performance across different urban contexts and methodological approaches.

### 3.9 Data Export and Visualization

The results of the analysis were exported for further examination and visualization:

1. **Statistical Results**: A CSV file containing all correlation coefficients, derived weights, and validation metrics for each cluster and method.

2. **Downscaled LST Images**: GeoTIFF files of the downscaled LST (10m resolution) for each cluster, enabling detailed visual inspection and further analysis.

3. **Visualization**: The original and downscaled LST products were visualized with a consistent color palette (blue to red, representing 20-45°C) to facilitate visual comparison.

## References

Breiman, L. (2001). Random forests. Machine Learning, 45(1), 5-32.

Chen, X. L., Zhao, H. M., Li, P. X., & Yin, Z. Y. (2006). Remote sensing image-based analysis of the relationship between urban heat island and land use/cover changes. Remote Sensing of Environment, 104(2), 133-146.

Kumar, L., & Mutanga, O. (2018). Google Earth Engine applications since inception: Usage, trends, and potential. Remote Sensing, 10(10), 1509.

Mo, Y., Kang, S., Hwang, W. H., Li, F., Yan, X., & Ren, X. (2021). Spatiotemporal variations in land surface temperature and its relationship with climate factors and land cover over the Tibetan Plateau. International Journal of Climatology, 41(S1), E3425-E3444.
