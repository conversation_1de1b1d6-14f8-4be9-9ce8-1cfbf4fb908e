// ECOSTRESS Downscaling using Sentinel-2 data
// This script downscales ECOSTRESS LST (70m) to 10m using Sentinel-2 indices

// Define Berlin's coordinates and clusters
var berlin = ee.Geometry.Polygon([
  [13.088341, 52.338246], [13.761864, 52.338246],
  [13.761864, 52.675753], [13.088341, 52.675753], [13.088341, 52.338246]
]);

// Define the same 4 clusters as in the Landsat analysis
var cluster1 = ee.Geometry.Rectangle([13.41, 52.49, 13.45, 52.53]); // Urban Core (Kreuzberg)
var cluster2 = ee.Geometry.Rectangle([13.42, 52.56, 13.46, 52.60]); // Mixed Urban (Prenzlauer Berg)
var cluster3 = ee.Geometry.Rectangle([13.28, 52.47, 13.32, 52.51]); // Southwest Residential (Wilmersdorf)
var cluster4 = ee.Geometry.Rectangle([13.58, 52.41, 13.62, 52.45]); // Urban-Rural Transition (Köpenick)

var clusters = ee.FeatureCollection([
  ee.Feature(cluster1, {name: 'Urban Core (Kreuzberg)'}),
  ee.Feature(cluster2, {name: 'Mixed Urban (Prenzlauer Berg)'}),
  ee.Feature(cluster3, {name: 'Southwest Residential (Wilmersdorf)'}),
  ee.Feature(cluster4, {name: 'Urban-Rural Transition (Köpenick)'})
]);

// Load data collections (extending date range for better ECOSTRESS coverage)
var startDate = '2022-06-01', endDate = '2022-09-30';

// Load ECOSTRESS Land Surface Temperature data
var ecostress = ee.ImageCollection('ECOSTRESS/ECO2LSTE')
  .filterBounds(berlin)
  .filterDate(startDate, endDate);

// Load Sentinel-2 Surface Reflectance
var s2_sr = ee.ImageCollection('COPERNICUS/S2_SR')
  .filterBounds(berlin)
  .filterDate(startDate, endDate)
  .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 10));

// Print image information
print('ECOSTRESS images:', ecostress.size());
print('Sentinel-2 images:', s2_sr.size());

// Function to process ECOSTRESS data
var processEcostress = function(image) {
  // Extract LST band and convert to Celsius
  var lst = image.select('LST').multiply(0.02).subtract(273.15).rename('LST');

  // Extract quality band for masking
  var qa = image.select('QC');

  // Create quality mask (bits based on ECOSTRESS documentation)
  // Bits 0-1: LST quality (00=best, 01=good, 10=not produced due to cloud, 11=not produced other)
  var qualityMask = qa.bitwiseAnd(3).lte(1);  // Keep only best and good quality

  return lst.updateMask(qualityMask);
};

// Process ECOSTRESS collection
var ecostress_processed = ecostress.map(processEcostress);

// Process Sentinel-2 collection
var s2_processed = s2_sr
  .map(function(image) {
    // Mask clouds
    var qa = image.select('QA60');
    var mask = qa.bitwiseAnd(1 << 10).eq(0).and(qa.bitwiseAnd(1 << 11).eq(0));

    // Calculate indices
    var ndvi = image.normalizedDifference(['B8', 'B4']).rename('NDVI');
    var ndbi = image.normalizedDifference(['B11', 'B8']).rename('NDBI');
    var ndwi = image.normalizedDifference(['B3', 'B8']).rename('NDWI');
    var albedo = image.select('B2').multiply(0.160)
                .add(image.select('B4').multiply(0.291))
                .add(image.select('B8').multiply(0.243))
                .add(image.select('B11').multiply(0.116))
                .add(image.select('B12').multiply(0.112))
                .add(0.018).rename('ALBEDO');

    return image.updateMask(mask).addBands([ndvi, ndbi, ndwi, albedo]);
  });

// Function to fill gaps in LST
var fillLSTGaps = function(lst) {
  var kernel = ee.Kernel.square({radius: 5, units: 'pixels'});
  var validMask = lst.mask();
  var lstFilled = lst.focal_mean({kernel: kernel, iterations: 5});
  return lst.unmask(0).where(validMask.not(), lstFilled.unmask(0));
};

// Function to correct water temperature - forcing water to appear blue in thermal images
var correctWaterTemperature = function(lst, ndwi) {
  // Create a water mask using NDWI threshold
  var waterMask = ndwi.gt(0.2);

  // Force water to be at exactly 20°C (the minimum of our visualization range)
  // This guarantees water will appear blue in the visualization
  var waterTemp = ee.Number(20);

  // Apply the correction to water bodies
  return lst.where(waterMask, ee.Image.constant(waterTemp));
};

// Function for TLC downscaling of ECOSTRESS data
var tlcDownscale = function(lst, ndvi, ndbi, ndwi, albedo, weights, cluster) {
  // Resample LST to intermediate resolution
  var lst_coarse = lst.reproject({crs: lst.projection(), scale: 70});

  // Keep indices at 10m resolution
  var ndvi_fine = ndvi.reproject({crs: ndvi.projection(), scale: 10});
  var ndbi_fine = ndbi.reproject({crs: ndbi.projection(), scale: 10});
  var ndwi_fine = ndwi.reproject({crs: ndwi.projection(), scale: 10});
  var albedo_fine = albedo.reproject({crs: albedo.projection(), scale: 10});

  // Define kernel for local statistics
  var kernel = ee.Kernel.square({radius: 70, units: 'meters', normalize: true});

  // Calculate LST statistics
  var lst_mean = lst_coarse.reduceNeighborhood({reducer: ee.Reducer.mean(), kernel: kernel});
  var lst_stddev = lst_coarse.reduceNeighborhood({reducer: ee.Reducer.stdDev(), kernel: kernel});

  // Apply empirical weights to indices
  var combined_index = ndvi_fine.multiply(weights.ndvi)
    .add(ndbi_fine.multiply(weights.ndbi))
    .add(ndwi_fine.multiply(weights.ndwi))
    .add(albedo_fine.multiply(weights.albedo))
    .rename('combined_index');

  // Calculate statistics for combined index
  var combined_mean = combined_index.reduceNeighborhood({reducer: ee.Reducer.mean(), kernel: kernel});
  var combined_stddev = combined_index.reduceNeighborhood({reducer: ee.Reducer.stdDev(), kernel: kernel});

  // Calculate scaling factor
  var scaling_factor = lst_stddev.divide(combined_stddev);

  // Calculate final downscaled LST
  var downscaled_lst = combined_index.multiply(scaling_factor)
    .add(lst_mean.subtract(combined_mean.multiply(scaling_factor)));

  // Create a new single-band image with a consistent name
  var result = ee.Image.constant(0).rename('LST');
  result = result.where(ee.Image(1), downscaled_lst);

  // Apply water temperature correction
  result = correctWaterTemperature(result, ndwi_fine);

  return result;
};

// Function to analyze a cluster using ECOSTRESS
var analyzeClusterEcostress = function(cluster, clusterName) {
  print('\n----- ECOSTRESS Analysis for ' + clusterName + ' -----');

  // Create composites for this cluster from Sentinel-2
  var ndviComposite = s2_processed.select('NDVI').median().clip(cluster);
  var ndbiComposite = s2_processed.select('NDBI').median().clip(cluster);
  var ndwiComposite = s2_processed.select('NDWI').median().clip(cluster);
  var albedoComposite = s2_processed.select('ALBEDO').median().clip(cluster);

  // Create LST composite from ECOSTRESS
  var lstComposite = ecostress_processed.select('LST').median().clip(cluster).rename('LST');
  var lstOriginal = ecostress_processed.select('LST').median().clip(cluster).rename('LST');

  // Apply gap filling and water temperature correction
  lstComposite = fillLSTGaps(lstComposite);
  lstOriginal = correctWaterTemperature(lstOriginal, ndwiComposite);
  lstComposite = correctWaterTemperature(lstComposite, ndwiComposite);

  // Calculate correlations and weights
  var calculateCorrelation = function(lst, index) {
    var index_resampled = index.reproject({crs: lst.projection(), scale: 70});  // Use ECOSTRESS resolution
    var combined = ee.Image.cat([lst, index_resampled]);
    var stats = combined.reduceRegion({
      reducer: ee.Reducer.pearsonsCorrelation(),
      geometry: cluster, scale: 70, maxPixels: 1e13
    });
    return ee.Number(stats.get('correlation'));
  };

  var ndvi_corr = calculateCorrelation(lstComposite, ndviComposite);
  var ndbi_corr = calculateCorrelation(lstComposite, ndbiComposite);
  var ndwi_corr = calculateCorrelation(lstComposite, ndwiComposite);
  var albedo_corr = calculateCorrelation(lstComposite, albedoComposite);

  var total_abs = ndvi_corr.abs().add(ndbi_corr.abs()).add(ndwi_corr.abs()).add(albedo_corr.abs());

  // Store weights
  var weights = {
    ndvi: ndvi_corr.divide(total_abs),
    ndbi: ndbi_corr.divide(total_abs),
    ndwi: ndwi_corr.divide(total_abs),
    albedo: albedo_corr.divide(total_abs)
  };

  // Print correlation information
  print(clusterName + ' - Correlation with LST:');
  print('NDVI correlation:', ndvi_corr);
  print('NDBI correlation:', ndbi_corr);
  print('NDWI correlation:', ndwi_corr);
  print('ALBEDO correlation:', albedo_corr);

  // Apply TLC downscaling
  var lst_tlc = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, albedoComposite, weights, cluster);

  // Visualization parameters
  var lstViz = {min: 20, max: 45, palette: ['#0000FF', '#00FFFF', '#00FF00', '#FFFF00', '#FF0000']};

  // Add layers to map
  Map.addLayer(lstComposite.clip(cluster), lstViz, clusterName + ' ECOSTRESS Original (70m)', false);
  Map.addLayer(lst_tlc.clip(cluster), lstViz, clusterName + ' ECOSTRESS Downscaled (10m)', false);

  // Return results
  return {
    'cluster_name': clusterName,
    'ndvi_correlation': ndvi_corr,
    'ndbi_correlation': ndbi_corr,
    'ndwi_correlation': ndwi_corr,
    'albedo_correlation': albedo_corr,
    'ndvi_weight': ndvi_corr.abs().divide(total_abs),
    'ndbi_weight': ndbi_corr.abs().divide(total_abs),
    'ndwi_weight': ndwi_corr.abs().divide(total_abs),
    'albedo_weight': albedo_corr.abs().divide(total_abs),
    'lst_original': lstOriginal,
    'lst_tlc': lst_tlc
  };
};

// Analyze each cluster with ECOSTRESS
var eco_cluster1_results = analyzeClusterEcostress(cluster1, 'Urban Core (Kreuzberg)');
var eco_cluster2_results = analyzeClusterEcostress(cluster2, 'Mixed Urban (Prenzlauer Berg)');
var eco_cluster3_results = analyzeClusterEcostress(cluster3, 'Southwest Residential (Wilmersdorf)');
var eco_cluster4_results = analyzeClusterEcostress(cluster4, 'Urban-Rural Transition (Köpenick)');

// Display clusters on map
Map.centerObject(berlin, 10);
Map.addLayer(clusters, {color: 'red'}, 'Analysis Clusters');

// Add a legend
var legend = ui.Panel({style: {position: 'bottom-left', padding: '8px 15px'}});
var legendTitle = ui.Label({
  value: 'LST Legend (°C)',
  style: {fontWeight: 'bold', fontSize: '16px', margin: '0 0 4px 0', padding: '0'}
});
legend.add(legendTitle);

// Create legend rows
var makeRow = function(color, name) {
  var colorBox = ui.Label({style: {backgroundColor: color, padding: '8px', margin: '0 0 4px 0'}});
  var description = ui.Label({value: name, style: {margin: '0 0 4px 6px'}});
  return ui.Panel({widgets: [colorBox, description], layout: ui.Panel.Layout.Flow('horizontal')});
};

// Add color and temperature ranges
legend.add(makeRow('#0000FF', '20-25°C (Water)'));
legend.add(makeRow('#00FFFF', '25-30°C'));
legend.add(makeRow('#00FF00', '30-35°C'));
legend.add(makeRow('#FFFF00', '35-40°C'));
legend.add(makeRow('#FF0000', '40-45°C'));
Map.add(legend);

// Export results to Drive
Export.table.toDrive({
  collection: ee.FeatureCollection([
    ee.Feature(null, {
      'Cluster': 'Urban Core (Kreuzberg)',
      'NDVI_Correlation': eco_cluster1_results.ndvi_correlation,
      'NDBI_Correlation': eco_cluster1_results.ndbi_correlation,
      'NDWI_Correlation': eco_cluster1_results.ndwi_correlation,
      'ALBEDO_Correlation': eco_cluster1_results.albedo_correlation,
      'NDVI_Weight': eco_cluster1_results.ndvi_weight,
      'NDBI_Weight': eco_cluster1_results.ndbi_weight,
      'NDWI_Weight': eco_cluster1_results.ndwi_weight,
      'ALBEDO_Weight': eco_cluster1_results.albedo_weight
    }),
    ee.Feature(null, {
      'Cluster': 'Mixed Urban (Prenzlauer Berg)',
      'NDVI_Correlation': eco_cluster2_results.ndvi_correlation,
      'NDBI_Correlation': eco_cluster2_results.ndbi_correlation,
      'NDWI_Correlation': eco_cluster2_results.ndwi_correlation,
      'ALBEDO_Correlation': eco_cluster2_results.albedo_correlation,
      'NDVI_Weight': eco_cluster2_results.ndvi_weight,
      'NDBI_Weight': eco_cluster2_results.ndbi_weight,
      'NDWI_Weight': eco_cluster2_results.ndwi_weight,
      'ALBEDO_Weight': eco_cluster2_results.albedo_weight
    }),
    ee.Feature(null, {
      'Cluster': 'Southwest Residential (Wilmersdorf)',
      'NDVI_Correlation': eco_cluster3_results.ndvi_correlation,
      'NDBI_Correlation': eco_cluster3_results.ndbi_correlation,
      'NDWI_Correlation': eco_cluster3_results.ndwi_correlation,
      'ALBEDO_Correlation': eco_cluster3_results.albedo_correlation,
      'NDVI_Weight': eco_cluster3_results.ndvi_weight,
      'NDBI_Weight': eco_cluster3_results.ndbi_weight,
      'NDWI_Weight': eco_cluster3_results.ndwi_weight,
      'ALBEDO_Weight': eco_cluster3_results.albedo_weight
    }),
    ee.Feature(null, {
      'Cluster': 'Urban-Rural Transition (Köpenick)',
      'NDVI_Correlation': eco_cluster4_results.ndvi_correlation,
      'NDBI_Correlation': eco_cluster4_results.ndbi_correlation,
      'NDWI_Correlation': eco_cluster4_results.ndwi_correlation,
      'ALBEDO_Correlation': eco_cluster4_results.albedo_correlation,
      'NDVI_Weight': eco_cluster4_results.ndvi_weight,
      'NDBI_Weight': eco_cluster4_results.ndbi_weight,
      'NDWI_Weight': eco_cluster4_results.ndwi_weight,
      'ALBEDO_Weight': eco_cluster4_results.albedo_weight
    })
  ]),
  description: 'ECOSTRESS_Downscaling_Results',
  fileFormat: 'CSV'
});

// Export original ECOSTRESS LST (70m) images for each cluster
Export.image.toDrive({
  image: eco_cluster1_results.lst_original.reproject({crs: 'EPSG:4326', scale: 70}),
  description: 'ECOSTRESS_Original_Urban_Core',
  scale: 70,
  region: cluster1,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: eco_cluster2_results.lst_original.reproject({crs: 'EPSG:4326', scale: 70}),
  description: 'ECOSTRESS_Original_Mixed_Urban',
  scale: 70,
  region: cluster2,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: eco_cluster3_results.lst_original.reproject({crs: 'EPSG:4326', scale: 70}),
  description: 'ECOSTRESS_Original_Southwest_Residential',
  scale: 70,
  region: cluster3,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: eco_cluster4_results.lst_original.reproject({crs: 'EPSG:4326', scale: 70}),
  description: 'ECOSTRESS_Original_Urban_Rural_Transition',
  scale: 70,
  region: cluster4,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

// Export downscaled ECOSTRESS LST (10m) images for each cluster
Export.image.toDrive({
  image: eco_cluster1_results.lst_tlc,
  description: 'ECOSTRESS_Downscaled_Urban_Core',
  scale: 10,
  region: cluster1,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: eco_cluster2_results.lst_tlc,
  description: 'ECOSTRESS_Downscaled_Mixed_Urban',
  scale: 10,
  region: cluster2,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: eco_cluster3_results.lst_tlc,
  description: 'ECOSTRESS_Downscaled_Southwest_Residential',
  scale: 10,
  region: cluster3,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: eco_cluster4_results.lst_tlc,
  description: 'ECOSTRESS_Downscaled_Urban_Rural_Transition',
  scale: 10,
  region: cluster4,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});
