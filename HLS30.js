// HLS30.js - Image browser for HLSL30, HLSS30 and Sentinel-2 data with NDVI and LST analysis (2013 and 2024)

// Define Berlin area of interest
var berlin = ee.Geometry.Rectangle([13.088341, 52.338246, 13.761864, 52.675753]);

// Define time periods for analysis
var periods = [
  {year: 2013, startDate: '2013-07-01', endDate: '2013-08-31'},
  {year: 2024, startDate: '2024-05-01', endDate: '2024-06-30'}
];

// Function to calculate NDVI for HLS Landsat data
function processHLSL(collection) {
  return collection.map(function(image) {
    var ndvi = image.normalizedDifference(['B5', 'B4']).rename('NDVI');
    return image.addBands(ndvi);
  });
}

// Function to calculate NDVI for HLS Sentinel data
function processHLSS(collection) {
  return collection.map(function(image) {
    var ndvi = image.normalizedDifference(['B5', 'B4']).rename('NDVI');
    return image.addBands(ndvi);
  });
}

// Function to calculate NDVI for Sentinel-2 data
function processS2(collection) {
  return collection.map(function(image) {
    var ndvi = image.normalizedDifference(['B8', 'B4']).rename('NDVI');
    return image.addBands(ndvi);
  });
}

// Function to get LST from Landsat data
function getLandsatLST(year, startDate, endDate) {
  var collection;

  if (year < 2020) {
    // For 2013, use Landsat 8
    collection = ee.ImageCollection('LANDSAT/LC08/C02/T1_L2')
      .filterBounds(berlin)
      .filterDate(startDate, endDate)
      .filterMetadata('CLOUD_COVER', 'less_than', 20);
  } else {
    // For 2024, use Landsat 9
    collection = ee.ImageCollection('LANDSAT/LC09/C02/T1_L2')
      .filterBounds(berlin)
      .filterDate(startDate, endDate)
      .filterMetadata('CLOUD_COVER', 'less_than', 20);
  }

  // Process LST
  var processedLST = collection.map(function(image) {
    // Get the thermal band
    var thermal = image.select('ST_B10');

    // Convert to Celsius
    var lst = thermal.multiply(0.00341802).add(149.0).subtract(273.15).rename('LST');

    // Cloud masking
    var qa = image.select('QA_PIXEL');
    var cloudMask = qa.bitwiseAnd(1 << 3).eq(0)
                      .and(qa.bitwiseAnd(1 << 4).eq(0));

    return lst.updateMask(cloudMask);
  });

  return processedLST;
}

// Function to downscale LST using NDVI
function downscaleLST(lst, ndvi) {
  // Resample LST to 100m (intermediate resolution)
  var lst_100m = lst.reproject({
    crs: lst.projection(),
    scale: 100
  });

  // Resample NDVI to 30m
  var ndvi_30m = ndvi.reproject({
    crs: ndvi.projection(),
    scale: 30
  });

  // Calculate statistics in a neighborhood
  var kernel = ee.Kernel.square({
    radius: 3,
    units: 'pixels',
    normalize: true
  });

  // Calculate LST statistics
  var lst_mean = lst_100m.reduceNeighborhood({
    reducer: ee.Reducer.mean(),
    kernel: kernel
  });

  var lst_stdDev = lst_100m.reduceNeighborhood({
    reducer: ee.Reducer.stdDev(),
    kernel: kernel
  });

  // Calculate NDVI statistics
  var ndvi_mean = ndvi_30m.reduceNeighborhood({
    reducer: ee.Reducer.mean(),
    kernel: kernel
  });

  var ndvi_stdDev = ndvi_30m.reduceNeighborhood({
    reducer: ee.Reducer.stdDev(),
    kernel: kernel
  });

  // Calculate scaling factor
  var scaling_factor = lst_stdDev.divide(ndvi_stdDev);

  // Downscale LST to 30m
  var lst_30m = ndvi_30m.subtract(ndvi_mean)
                .multiply(scaling_factor)
                .add(lst_mean);

  return lst_30m.rename('LST_30m');
}

// Load 2013 data (only HLSL30 available)
var hlsl2013 = ee.ImageCollection('NASA/HLS/HLSL30/v002')
  .filterBounds(berlin)
  .filterDate(periods[0].startDate, periods[0].endDate)
  .filter(ee.Filter.lt('CLOUD_COVERAGE', 10));

// Process 2013 data
var hlsl2013_processed = processHLSL(hlsl2013);
var ndvi2013_hlsl = hlsl2013_processed.select('NDVI').median();

// Print info about 2013 collection
print('2013 HLSL30 collection size:', hlsl2013.size());
if (hlsl2013.size().gt(0)) {
  print('2013 HLSL30 band names:', hlsl2013.first().bandNames());
  // Print a sample image date for reference
  print('2013 HLSL30 sample date:', ee.Date(hlsl2013.first().get('system:time_start')).format('YYYY-MM-dd'));
}

// Load 2024 data (all three collections)
var hlsl2024 = ee.ImageCollection('NASA/HLS/HLSL30/v002')
  .filterBounds(berlin)
  .filterDate(periods[1].startDate, periods[1].endDate)
  .filter(ee.Filter.lt('CLOUD_COVERAGE', 10));

var hlss2024 = ee.ImageCollection('NASA/HLS/HLSS30/v002')
  .filterBounds(berlin)
  .filterDate(periods[1].startDate, periods[1].endDate)
  .filter(ee.Filter.lt('CLOUD_COVERAGE', 10));

var s2_2024 = ee.ImageCollection('COPERNICUS/S2_SR')
  .filterBounds(berlin)
  .filterDate(periods[1].startDate, periods[1].endDate)
  .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 10));

// Process 2024 data
var hlsl2024_processed = processHLSL(hlsl2024);
var hlss2024_processed = processHLSS(hlss2024);
var s2_2024_processed = processS2(s2_2024);

// Create median composites for 2024
var ndvi2024_hlsl = hlsl2024_processed.select('NDVI').median();
var ndvi2024_hlss = hlss2024_processed.select('NDVI').median();
var ndvi2024_s2 = s2_2024_processed.select('NDVI').median();

// Print info about 2024 collections
print('2024 HLSL30 collection size:', hlsl2024.size());
print('2024 HLSS30 collection size:', hlss2024.size());
print('2024 Sentinel-2 collection size:', s2_2024.size());

if (hlsl2024.size().gt(0)) {
  print('2024 HLSL30 band names:', hlsl2024.first().bandNames());
}
if (hlss2024.size().gt(0)) {
  print('2024 HLSS30 band names:', hlss2024.first().bandNames());
}
if (s2_2024.size().gt(0)) {
  print('2024 Sentinel-2 band names:', s2_2024.first().bandNames());
}

// Set up the map
Map.centerObject(berlin, 10);

// NDVI visualization parameters
var ndviViz = {
  min: -0.2,
  max: 0.8,
  palette: ['blue', 'white', 'green']
};

// Calculate NDVI differences between 2013 and 2024
// For HLSL30 (the only collection available for both years)
var ndvi_diff_hlsl = ndvi2024_hlsl.subtract(ndvi2013_hlsl).rename('NDVI_diff');

// Calculate statistics for the difference
var diffStats = ndvi_diff_hlsl.reduceRegion({
  reducer: ee.Reducer.mean().combine({
    reducer2: ee.Reducer.stdDev(),
    sharedInputs: true
  }).combine({
    reducer2: ee.Reducer.minMax(),
    sharedInputs: true
  }),
  geometry: berlin,
  scale: 30,
  maxPixels: 1e13
});

// Print difference statistics
print('NDVI Change Statistics (2024 - 2013):');
print('  Mean change:', diffStats.get('NDVI_diff_mean'));
print('  StdDev:', diffStats.get('NDVI_diff_stdDev'));
print('  Min change:', diffStats.get('NDVI_diff_min'));
print('  Max change:', diffStats.get('NDVI_diff_max'));

// Load and process LST data
var lst2013 = getLandsatLST(2013, periods[0].startDate, periods[0].endDate);
var lst2024 = getLandsatLST(2024, periods[1].startDate, periods[1].endDate);

// Create LST composites
var lst2013_composite = lst2013.median();
var lst2024_composite = lst2024.median();

// Print LST collection information
print('2013 LST collection size:', lst2013.size());
print('2024 LST collection size:', lst2024.size());

// Downscale LST using NDVI
var lst2013_30m = downscaleLST(lst2013_composite, ndvi2013_hlsl);
var lst2024_30m = downscaleLST(lst2024_composite, ndvi2024_hlsl);

// Calculate LST difference
var lst_diff = lst2024_30m.subtract(lst2013_30m).rename('LST_diff');

// Calculate statistics for LST difference
var lstDiffStats = lst_diff.reduceRegion({
  reducer: ee.Reducer.mean().combine({
    reducer2: ee.Reducer.stdDev(),
    sharedInputs: true
  }).combine({
    reducer2: ee.Reducer.minMax(),
    sharedInputs: true
  }),
  geometry: berlin,
  scale: 30,
  maxPixels: 1e13
});

// Print LST difference statistics
print('LST Change Statistics (2024 - 2013):');
print('  Mean change (°C):', lstDiffStats.get('LST_diff_mean'));
print('  StdDev (°C):', lstDiffStats.get('LST_diff_stdDev'));
print('  Min change (°C):', lstDiffStats.get('LST_diff_min'));
print('  Max change (°C):', lstDiffStats.get('LST_diff_max'));

// Visualization parameters for NDVI
// Custom color scheme: water (blue), streets/buildings (red), bare land/dry vegetation (yellow), vegetation (green)
var ndviViz = {
  min: -0.2,
  max: 0.8,
  palette: [
    '0000FF', // Blue for water (NDVI < 0)
    'FF0000', // Red for streets/buildings (low NDVI)
    'FFFF00', // Yellow for bare land/dry vegetation (medium NDVI)
    '00FF00', // Green for vegetation/parks (high NDVI)
    '005500'  // Dark green for dense vegetation (very high NDVI)
  ]
};

// Visualization parameters for NDVI difference
var diffViz = {
  min: -0.3,
  max: 0.3,
  palette: [
    'FF0000', // Red for vegetation loss
    'FFFFFF', // White for no change
    '00FF00'  // Green for vegetation gain
  ]
};

// Visualization parameters for LST
var lstViz = {
  min: 20,
  max: 40,
  palette: [
    '0000FF', // Blue (cold)
    '00FFFF', // Cyan
    'FFFF00', // Yellow
    'FF0000', // Red (hot)
    'DarkRed'  // Dark red (very hot)
  ]
};

// Visualization parameters for LST difference
var lstDiffViz = {
  min: -5,
  max: 5,
  palette: [
    '0000FF', // Blue (cooling)
    'FFFFFF', // White (no change)
    'FF0000'  // Red (warming)
  ]
};

// Add NDVI layers to map
Map.addLayer(ndvi2013_hlsl, ndviViz, '2013 NDVI (HLSL30)', true);
Map.addLayer(ndvi2024_hlsl, ndviViz, '2024 NDVI (HLSL30)', false);
Map.addLayer(ndvi2024_hlss, ndviViz, '2024 NDVI (HLSS30)', false);
Map.addLayer(ndvi2024_s2, ndviViz, '2024 NDVI (Sentinel-2)', false);
Map.addLayer(ndvi_diff_hlsl, diffViz, 'NDVI Change (2024-2013)', false);

// Add LST layers to map
Map.addLayer(lst2013_composite, lstViz, '2013 LST (100m)', false);
Map.addLayer(lst2024_composite, lstViz, '2024 LST (100m)', false);
Map.addLayer(lst2013_30m, lstViz, '2013 LST (30m downscaled)', false);
Map.addLayer(lst2024_30m, lstViz, '2024 LST (30m downscaled)', false);
Map.addLayer(lst_diff, lstDiffViz, 'LST Change (2024-2013)', false);

// Add a dropdown for selecting which layer to view
var layerSelector = ui.Select({
  items: [
    '2013 NDVI (HLSL30)',
    '2024 NDVI (HLSL30)',
    '2024 NDVI (HLSS30)',
    '2024 NDVI (Sentinel-2)',
    'NDVI Change (2024-2013)',
    '2013 LST (100m)',
    '2024 LST (100m)',
    '2013 LST (30m downscaled)',
    '2024 LST (30m downscaled)',
    'LST Change (2024-2013)'
  ],
  value: '2013 NDVI (HLSL30)',
  onChange: function(selected) {
    // Hide all layers first
    for (var i = 0; i < 10; i++) {
      if (Map.layers().length() > i) {
        Map.layers().get(i).setShown(false);
      }
    }

    // Show the selected layer
    var layerIndex = {
      '2013 NDVI (HLSL30)': 0,
      '2024 NDVI (HLSL30)': 1,
      '2024 NDVI (HLSS30)': 2,
      '2024 NDVI (Sentinel-2)': 3,
      'NDVI Change (2024-2013)': 4,
      '2013 LST (100m)': 5,
      '2024 LST (100m)': 6,
      '2013 LST (30m downscaled)': 7,
      '2024 LST (30m downscaled)': 8,
      'LST Change (2024-2013)': 9
    };

    Map.layers().get(layerIndex[selected]).setShown(true);
  }
});

// Add a panel with the layer selector
Map.add(ui.Panel({
  widgets: [ui.Label('Select Layer:'), layerSelector],
  style: {position: 'top-right'}
}));

// Add a legend for LST
var lstLegend = ui.Panel({
  style: {
    position: 'bottom-right',
    padding: '8px 15px'
  }
});

var lstLegendTitle = ui.Label({
  value: 'LST Legend (°C)',
  style: {
    fontWeight: 'bold',
    fontSize: '16px',
    margin: '0 0 4px 0',
    padding: '0'
  }
});

lstLegend.add(lstLegendTitle);

// Function to create a legend row
var makeRow = function(color, name) {
  var colorBox = ui.Label({
    style: {
      backgroundColor: color,
      padding: '8px',
      margin: '0 0 4px 0'
    }
  });

  var description = ui.Label({
    value: name,
    style: {margin: '0 0 4px 6px'}
  });

  return ui.Panel({
    widgets: [colorBox, description],
    layout: ui.Panel.Layout.Flow('horizontal')
  });
};

// Add color and temperature ranges for LST
lstLegend.add(makeRow('0000FF', '20°C (Cold)'));
lstLegend.add(makeRow('00FFFF', '25°C'));
lstLegend.add(makeRow('FFFF00', '30°C'));
lstLegend.add(makeRow('FF0000', '35°C'));
lstLegend.add(makeRow('8B0000', '40°C (Hot)'));

// Add legend to map
Map.add(lstLegend);
