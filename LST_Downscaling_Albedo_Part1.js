// Define Berlin's coordinates (approximate bounding box)
var berlin_bbox = [
  [13.088341, 52.338246],  // SW corner
  [13.761864, 52.338246],  // SE corner
  [13.761864, 52.675753],  // NE corner
  [13.088341, 52.675753],  // NW corner
  [13.088341, 52.338246]   // SW corner again to close the polygon
];

// Create Berlin area of interest (aoi) geometry
var berlin = ee.Geometry.Polygon(berlin_bbox);

// Define 4 non-intersecting clusters within Berlin representing different urban contexts
// Cluster 1: City center with dense urban fabric (Mitte - adjusted to avoid cloud pixels)
var cluster1 = ee.Geometry.Rectangle([13.37, 52.52, 13.41, 52.55]);

// Cluster 2: Mixed urban area (Prenzlauer Berg)
var cluster2 = ee.Geometry.Rectangle([13.42, 52.56, 13.46, 52.59]);

// Cluster 3: Southwest residential area (Wilmersdorf - new cluster)
var cluster3 = ee.Geometry.Rectangle([13.28, 52.47, 13.32, 52.50]);

// Cluster 4: Urban-rural transition zone (Köpenick)
var cluster4 = ee.Geometry.Rectangle([13.58, 52.41, 13.62, 52.44]);

// Create a feature collection of clusters for visualization and analysis
var clusters = ee.FeatureCollection([
  ee.Feature(cluster1, {name: 'City Center (Mitte)'}),
  ee.Feature(cluster2, {name: 'Mixed Urban (Prenzlauer Berg)'}),
  ee.Feature(cluster3, {name: 'Southwest Residential (Wilmersdorf)'}),
  ee.Feature(cluster4, {name: 'Urban-Rural Transition (Köpenick)'})
]);

// Define time period with more options to ensure cloud-free coverage
var startDate = '2022-07-01';
var endDate = '2022-08-31';

// Load Landsat 8 Collection 2 Level 2 (Surface Reflectance and Surface Temperature)
var l8_sr = ee.ImageCollection('LANDSAT/LC08/C02/T1_L2')
               .filterBounds(berlin)
               .filterDate(startDate, endDate)
               .filterMetadata('CLOUD_COVER', 'less_than', 5);

// Load Sentinel-2 Surface Reflectance
var s2_sr = ee.ImageCollection('COPERNICUS/S2_SR')
               .filterBounds(berlin)
               .filterDate(startDate, endDate)
               .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 5));

// Function to extract image information
var getImageInfo = function(image) {
  return {
    'Date': ee.Date(image.get('system:time_start')).format('YYYY-MM-dd'),
    'Cloud_Cover': image.get('CLOUD_COVER'),
    'Scene_ID': image.get('system:index')
  };
};

// Print detailed information about available images
print('Available Images Information:');
print('1. Landsat 8 Images:');
var l8_list = l8_sr.map(function(image) {
  return ee.Feature(null, getImageInfo(image));
});
print('Number of Landsat 8 images:', l8_sr.size());
print('Landsat 8 image details:', l8_list);

print('\n2. Sentinel-2 Images:');
var s2_list = s2_sr.map(function(image) {
  return ee.Feature(null, {
    'Date': ee.Date(image.get('system:time_start')).format('YYYY-MM-dd'),
    'Cloud_Cover': image.get('CLOUDY_PIXEL_PERCENTAGE'),
    'Scene_ID': image.get('system:index')
  });
});
print('Number of Sentinel-2 images:', s2_sr.size());
print('Sentinel-2 image details:', s2_list);

// Function to mask clouds in Sentinel-2
var maskS2clouds = function(image) {
  var qa = image.select('QA60');
  var cloudBitMask = 1 << 10;
  var cirrusBitMask = 1 << 11;
  var mask = qa.bitwiseAnd(cloudBitMask).eq(0)
      .and(qa.bitwiseAnd(cirrusBitMask).eq(0));
  return image.updateMask(mask);
};

// Function to calculate spectral indices for Sentinel-2
var addIndices_S2 = function(image) {
  // Calculate NDVI, NDBI, NDWI
  var ndvi = image.normalizedDifference(['B8', 'B4']).rename('NDVI');
  var ndbi = image.normalizedDifference(['B11', 'B8']).rename('NDBI');
  var ndwi = image.normalizedDifference(['B3', 'B8']).rename('NDWI');

  // Calculate broadband albedo using Liang's equation for Sentinel-2
  // Liang, S. (2001). Narrowband to broadband conversions of land surface albedo I: Algorithms
  var albedo = image.select('B2').multiply(0.160)
                .add(image.select('B4').multiply(0.291))
                .add(image.select('B8').multiply(0.243))
                .add(image.select('B11').multiply(0.116))
                .add(image.select('B12').multiply(0.112))
                .add(0.018)
                .rename('ALBEDO');

  return image.addBands([ndvi, ndbi, ndwi, albedo]);
};

// Function to process Landsat 8 thermal data
var processLandsat = function(image) {
  var thermal = image.select('ST_B10')
                     .multiply(0.00341802)
                     .add(149.0)
                     .subtract(273.15)
                     .rename('LST');

  var qa = image.select('QA_PIXEL');
  var cloudMask = qa.bitwiseAnd(1 << 3).eq(0)
                    .and(qa.bitwiseAnd(1 << 4).eq(0));

  return thermal.updateMask(cloudMask);
};

// Process collections
var s2_processed = s2_sr
  .map(maskS2clouds)
  .map(addIndices_S2);

var l8_processed = l8_sr
  .map(processLandsat);

// Function to fill gaps in LST using neighboring values
var fillLSTGaps = function(lst) {
  // Create a kernel for focal mean
  var kernel = ee.Kernel.square({radius: 3, units: 'pixels'});

  // Create a mask of valid pixels
  var validMask = lst.mask();

  // Calculate focal mean
  var lstFilled = lst.focal_mean({
    kernel: kernel,
    iterations: 3
  });

  // Use original values where available, filled values elsewhere
  return lst.unmask(0).where(validMask.not(), lstFilled.unmask(0));
};

// Function to analyze a single cluster
var analyzeCluster = function(cluster, clusterName) {
  print('\n----- Analysis for ' + clusterName + ' -----');

  // Create composites for this cluster
  var ndviComposite = s2_processed.select('NDVI').median().clip(cluster);
  var ndbiComposite = s2_processed.select('NDBI').median().clip(cluster);
  var ndwiComposite = s2_processed.select('NDWI').median().clip(cluster);
  var albedoComposite = s2_processed.select('ALBEDO').median().clip(cluster);

  // Create LST composite and fill any gaps
  var lstComposite = l8_processed.select('LST').median().clip(cluster);
  lstComposite = fillLSTGaps(lstComposite);

  // Calculate statistics for each index
  var calculateStats = function(lst, index, name) {
    // Resample index to LST resolution for proper comparison
    var index_resampled = index.reproject({
      crs: lst.projection(),
      scale: 100
    });

    // Combine LST and index
    var combined = ee.Image.cat([lst, index_resampled]);

    // Calculate correlation
    var stats = combined.reduceRegion({
      reducer: ee.Reducer.pearsonsCorrelation(),
      geometry: cluster,
      scale: 100,
      maxPixels: 1e13
    });

    // Calculate basic statistics for each
    var lst_stats = lst.reduceRegion({
      reducer: ee.Reducer.mean().combine({
        reducer2: ee.Reducer.stdDev(),
        sharedInputs: true
      }),
      geometry: cluster,
      scale: 100,
      maxPixels: 1e13
    });

    var index_stats = index.reduceRegion({
      reducer: ee.Reducer.mean().combine({
        reducer2: ee.Reducer.stdDev(),
        sharedInputs: true
      }),
      geometry: cluster,
      scale: 10,
      maxPixels: 1e13
    });

    return ee.Dictionary({
      'Index': name,
      'Correlation': stats.get('correlation'),
      'LST_mean': lst_stats.get('LST_mean'),
      'LST_stdDev': lst_stats.get('LST_stdDev'),
      'Index_mean': index_stats.get(name + '_mean'),
      'Index_stdDev': index_stats.get(name + '_stdDev')
    });
  };

  // Calculate statistics for each index
  var ndvi_stats = calculateStats(lstComposite, ndviComposite, 'NDVI');
  var ndbi_stats = calculateStats(lstComposite, ndbiComposite, 'NDBI');
  var ndwi_stats = calculateStats(lstComposite, ndwiComposite, 'NDWI');
  var albedo_stats = calculateStats(lstComposite, albedoComposite, 'ALBEDO');

  print('NDVI Statistics:', ndvi_stats);
  print('NDBI Statistics:', ndbi_stats);
  print('NDWI Statistics:', ndwi_stats);
  print('ALBEDO Statistics:', albedo_stats);

  // Calculate and print weights based on correlations
  var ndvi_corr = ee.Number(ndvi_stats.get('Correlation'));
  var ndbi_corr = ee.Number(ndbi_stats.get('Correlation'));
  var ndwi_corr = ee.Number(ndwi_stats.get('Correlation'));
  var albedo_corr = ee.Number(albedo_stats.get('Correlation'));

  var total_abs = ndvi_corr.abs()
    .add(ndbi_corr.abs())
    .add(ndwi_corr.abs())
    .add(albedo_corr.abs());

  print('Calculated Weights:');
  print('Total absolute correlation:', total_abs);
  print('NDVI weight:', ndvi_corr.abs().divide(total_abs));
  print('NDBI weight:', ndbi_corr.abs().divide(total_abs));
  print('NDWI weight:', ndwi_corr.abs().divide(total_abs));
  print('ALBEDO weight:', albedo_corr.abs().divide(total_abs));

  // Store empirically derived weights
  var weights = {
    ndvi: ndvi_corr.divide(total_abs),
    ndbi: ndbi_corr.divide(total_abs),
    ndwi: ndwi_corr.divide(total_abs),
    albedo: albedo_corr.divide(total_abs)
  };

  // TLC Downscaling Implementation with empirical weights
  var tlcDownscale = function(lst, ndvi, ndbi, ndwi, albedo, weights) {
    // Resample LST to intermediate resolution (90m)
    var lst_coarse = lst.reproject({
      crs: lst.projection(),
      scale: 90
    });

    // Keep indices at 10m resolution
    var ndvi_fine = ndvi.reproject({
      crs: ndvi.projection(),
      scale: 10
    });
    var ndbi_fine = ndbi.reproject({
      crs: ndbi.projection(),
      scale: 10
    });
    var ndwi_fine = ndwi.reproject({
      crs: ndwi.projection(),
      scale: 10
    });
    var albedo_fine = albedo.reproject({
      crs: albedo.projection(),
      scale: 10
    });

    // Define kernel for local statistics
    var kernel = ee.Kernel.square({
      radius: 90,
      units: 'meters',
      normalize: true
    });

    // Calculate LST statistics
    var lst_mean = lst_coarse.reduceNeighborhood({
      reducer: ee.Reducer.mean(),
      kernel: kernel
    });

    var lst_stddev = lst_coarse.reduceNeighborhood({
      reducer: ee.Reducer.stdDev(),
      kernel: kernel
    });

    // Apply empirical weights to indices
    var combined_index = ndvi_fine.multiply(weights.ndvi)
      .add(ndbi_fine.multiply(weights.ndbi))
      .add(ndwi_fine.multiply(weights.ndwi))
      .add(albedo_fine.multiply(weights.albedo))
      .rename('combined_index');

    // Calculate statistics for combined index
    var combined_mean = combined_index.reduceNeighborhood({
      reducer: ee.Reducer.mean(),
      kernel: kernel
    });

    var combined_stddev = combined_index.reduceNeighborhood({
      reducer: ee.Reducer.stdDev(),
      kernel: kernel
    });

    // Calculate scaling factor
    var scaling_factor = lst_stddev.divide(combined_stddev);

    // Calculate final downscaled LST
    var downscaled_lst = combined_index.multiply(scaling_factor)
      .add(lst_mean.subtract(combined_mean.multiply(scaling_factor)))
      .rename('LST_TLC');

    return downscaled_lst;
  };

  // Apply TLC downscaling with empirical weights
  var lst_tlc = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, albedoComposite, weights);

  // Also apply single-index downscaling for comparison
  var weights_ndvi_only = {ndvi: ee.Number(1), ndbi: ee.Number(0), ndwi: ee.Number(0), albedo: ee.Number(0)};
  var weights_ndbi_only = {ndvi: ee.Number(0), ndbi: ee.Number(1), ndwi: ee.Number(0), albedo: ee.Number(0)};
  var weights_ndwi_only = {ndvi: ee.Number(0), ndbi: ee.Number(0), ndwi: ee.Number(1), albedo: ee.Number(0)};
  var weights_albedo_only = {ndvi: ee.Number(0), ndbi: ee.Number(0), ndwi: ee.Number(0), albedo: ee.Number(1)};

  var lst_ndvi_only = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, albedoComposite, weights_ndvi_only);
  var lst_ndbi_only = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, albedoComposite, weights_ndbi_only);
  var lst_ndwi_only = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, albedoComposite, weights_ndwi_only);
  var lst_albedo_only = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, albedoComposite, weights_albedo_only);

  // Validation function
  var validateDownscaling = function(original, downscaled, region, name) {
    // Resample downscaled to original resolution for comparison
    var downscaled_resampled = downscaled.reproject({
      crs: original.projection(),
      scale: 100
    });

    var diff = downscaled_resampled.subtract(original);

    // Calculate RMSE
    var rmse = diff.pow(2).reduceRegion({
      reducer: ee.Reducer.mean(),
      geometry: region,
      scale: 100,
      maxPixels: 1e13
    }).get('LST_TLC');

    rmse = ee.Number(rmse).sqrt();

    // Calculate correlation between original and downscaled
    var combined = ee.Image.cat([original, downscaled_resampled]);
    var corr = combined.reduceRegion({
      reducer: ee.Reducer.pearsonsCorrelation(),
      geometry: region,
      scale: 100,
      maxPixels: 1e13
    }).get('correlation');

    // Calculate R-squared
    var originalMean = original.reduceRegion({
      reducer: ee.Reducer.mean(),
      geometry: region,
      scale: 100,
      maxPixels: 1e13
    }).get('LST');

    var totalSumSquares = original.subtract(ee.Image.constant(originalMean))
      .pow(2)
      .reduceRegion({
        reducer: ee.Reducer.sum(),
        geometry: region,
        scale: 100,
        maxPixels: 1e13
      }).get('LST');

    var residualSumSquares = diff.pow(2).reduceRegion({
      reducer: ee.Reducer.sum(),
      geometry: region,
      scale: 100,
      maxPixels: 1e13
    }).get('LST_TLC');

    var rSquared = ee.Number(1).subtract(ee.Number(residualSumSquares).divide(ee.Number(totalSumSquares)));

    print(name + ' Validation Metrics:');
    print('RMSE:', rmse);
    print('Correlation:', corr);
    print('R-squared:', rSquared);

    return {
      'Method': name,
      'RMSE': rmse,
      'Correlation': corr,
      'R_squared': rSquared
    };
  };
