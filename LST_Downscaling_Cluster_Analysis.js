// Define Berlin's coordinates (approximate bounding box)
var berlin_bbox = [
  [13.088341, 52.338246],  // SW corner
  [13.761864, 52.338246],  // SE corner
  [13.761864, 52.675753],  // NE corner
  [13.088341, 52.675753],  // NW corner
  [13.088341, 52.338246]   // SW corner again to close the polygon
];

// Create Berlin area of interest (aoi) geometry
var berlin = ee.Geometry.Polygon(berlin_bbox);

// Define 4 non-intersecting clusters within Berlin representing different urban contexts
// Cluster 1: City center with dense urban fabric (Alexanderplatz area)
var cluster1 = ee.Geometry.Rectangle([13.41, 52.52, 13.45, 52.55]);

// Cluster 2: Mixed urban area (Prenzlauer Berg)
var cluster2 = ee.Geometry.Rectangle([13.42, 52.56, 13.46, 52.59]);

// Cluster 3: Residential area with moderate density (Steglitz)
var cluster3 = ee.Geometry.Rectangle([13.30, 52.41, 13.34, 52.44]);

// Cluster 4: Urban-rural transition zone (<PERSON><PERSON><PERSON>ick)
var cluster4 = ee.Geometry.Rectangle([13.58, 52.41, 13.62, 52.44]);

// Create a feature collection of clusters for visualization and analysis
var clusters = ee.FeatureCollection([
  ee.Feature(cluster1, {name: 'City Center (Alexanderplatz)'}),
  ee.Feature(cluster2, {name: 'Mixed Urban (Prenzlauer Berg)'}),
  ee.Feature(cluster3, {name: 'Residential (Steglitz)'}),
  ee.Feature(cluster4, {name: 'Urban-Rural Transition (Köpenick)'})
]);

// Define time period
var startDate = '2022-08-09';
var endDate = '2022-08-13';

// Load Landsat 8 Collection 2 Level 2 (Surface Reflectance and Surface Temperature)
var l8_sr = ee.ImageCollection('LANDSAT/LC08/C02/T1_L2')
               .filterBounds(berlin)
               .filterDate(startDate, endDate)
               .filterMetadata('CLOUD_COVER', 'less_than', 10);

// Load Sentinel-2 Surface Reflectance
var s2_sr = ee.ImageCollection('COPERNICUS/S2_SR')
               .filterBounds(berlin)
               .filterDate(startDate, endDate)
               .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 10));

// Function to extract image information
var getImageInfo = function(image) {
  return {
    'Date': ee.Date(image.get('system:time_start')).format('YYYY-MM-dd'),
    'Cloud_Cover': image.get('CLOUD_COVER'),
    'Scene_ID': image.get('system:index')
  };
};

// Print detailed information about available images
print('Available Images Information:');
print('1. Landsat 8 Images:');
var l8_list = l8_sr.map(function(image) {
  return ee.Feature(null, getImageInfo(image));
});
print('Number of Landsat 8 images:', l8_sr.size());
print('Landsat 8 image details:', l8_list);

print('\n2. Sentinel-2 Images:');
var s2_list = s2_sr.map(function(image) {
  return ee.Feature(null, {
    'Date': ee.Date(image.get('system:time_start')).format('YYYY-MM-dd'),
    'Cloud_Cover': image.get('CLOUDY_PIXEL_PERCENTAGE'),
    'Scene_ID': image.get('system:index')
  });
});
print('Number of Sentinel-2 images:', s2_sr.size());
print('Sentinel-2 image details:', s2_list);

// Function to mask clouds in Sentinel-2
var maskS2clouds = function(image) {
  var qa = image.select('QA60');
  var cloudBitMask = 1 << 10;
  var cirrusBitMask = 1 << 11;
  var mask = qa.bitwiseAnd(cloudBitMask).eq(0)
      .and(qa.bitwiseAnd(cirrusBitMask).eq(0));
  return image.updateMask(mask);
};

// Function to calculate indices for Sentinel-2
var addIndices_S2 = function(image) {
  var ndvi = image.normalizedDifference(['B8', 'B4']).rename('NDVI');
  var ndbi = image.normalizedDifference(['B11', 'B8']).rename('NDBI');
  var ndwi = image.normalizedDifference(['B3', 'B8']).rename('NDWI');
  return image.addBands([ndvi, ndbi, ndwi]);
};

// Function to process Landsat 8 thermal data
var processLandsat = function(image) {
  var thermal = image.select('ST_B10')
                     .multiply(0.00341802)
                     .add(149.0)
                     .subtract(273.15)
                     .rename('LST');

  var qa = image.select('QA_PIXEL');
  var cloudMask = qa.bitwiseAnd(1 << 3).eq(0)
                    .and(qa.bitwiseAnd(1 << 4).eq(0));

  return thermal.updateMask(cloudMask);
};

// Process collections
var s2_processed = s2_sr
  .map(maskS2clouds)
  .map(addIndices_S2);

var l8_processed = l8_sr
  .map(processLandsat);

// Function to analyze a single cluster
var analyzeCluster = function(cluster, clusterName) {
  print('\n----- Analysis for ' + clusterName + ' -----');

  // Create composites for this cluster
  var ndviComposite = s2_processed.select('NDVI').median().clip(cluster);
  var ndbiComposite = s2_processed.select('NDBI').median().clip(cluster);
  var ndwiComposite = s2_processed.select('NDWI').median().clip(cluster);
  var lstComposite = l8_processed.select('LST').median().clip(cluster);

  // Calculate statistics for each index
  var calculateStats = function(lst, index, name) {
    // Resample index to LST resolution for proper comparison
    var index_resampled = index.reproject({
      crs: lst.projection(),
      scale: 100
    });

    // Combine LST and index
    var combined = ee.Image.cat([lst, index_resampled]);

    // Calculate correlation
    var stats = combined.reduceRegion({
      reducer: ee.Reducer.pearsonsCorrelation(),
      geometry: cluster,
      scale: 100,
      maxPixels: 1e13
    });

    // Calculate basic statistics for each
    var lst_stats = lst.reduceRegion({
      reducer: ee.Reducer.mean().combine({
        reducer2: ee.Reducer.stdDev(),
        sharedInputs: true
      }),
      geometry: cluster,
      scale: 100,
      maxPixels: 1e13
    });

    var index_stats = index.reduceRegion({
      reducer: ee.Reducer.mean().combine({
        reducer2: ee.Reducer.stdDev(),
        sharedInputs: true
      }),
      geometry: cluster,
      scale: 10,
      maxPixels: 1e13
    });

    return ee.Dictionary({
      'Index': name,
      'Correlation': stats.get('correlation'),
      'LST_mean': lst_stats.get('LST_mean'),
      'LST_stdDev': lst_stats.get('LST_stdDev'),
      'Index_mean': index_stats.get(name + '_mean'),
      'Index_stdDev': index_stats.get(name + '_stdDev')
    });
  };

  // Calculate statistics for each index
  var ndvi_stats = calculateStats(lstComposite, ndviComposite, 'NDVI');
  var ndbi_stats = calculateStats(lstComposite, ndbiComposite, 'NDBI');
  var ndwi_stats = calculateStats(lstComposite, ndwiComposite, 'NDWI');

  print('NDVI Statistics:', ndvi_stats);
  print('NDBI Statistics:', ndbi_stats);
  print('NDWI Statistics:', ndwi_stats);

  // Calculate and print weights based on correlations
  var ndvi_corr = ee.Number(ndvi_stats.get('Correlation'));
  var ndbi_corr = ee.Number(ndbi_stats.get('Correlation'));
  var ndwi_corr = ee.Number(ndwi_stats.get('Correlation'));

  var total_abs = ndvi_corr.abs()
    .add(ndbi_corr.abs())
    .add(ndwi_corr.abs());

  print('Calculated Weights:');
  print('Total absolute correlation:', total_abs);
  print('NDVI weight:', ndvi_corr.abs().divide(total_abs));
  print('NDBI weight:', ndbi_corr.abs().divide(total_abs));
  print('NDWI weight:', ndwi_corr.abs().divide(total_abs));

  // Store empirically derived weights
  var weights = {
    ndvi: ndvi_corr.divide(total_abs),
    ndbi: ndbi_corr.divide(total_abs),
    ndwi: ndwi_corr.divide(total_abs)
  };

  // TLC Downscaling Implementation with empirical weights
  var tlcDownscale = function(lst, ndvi, ndbi, ndwi, weights) {
    // Resample LST to intermediate resolution (90m)
    var lst_coarse = lst.reproject({
      crs: lst.projection(),
      scale: 90
    });

    // Keep indices at 10m resolution
    var ndvi_fine = ndvi.reproject({
      crs: ndvi.projection(),
      scale: 10
    });
    var ndbi_fine = ndbi.reproject({
      crs: ndbi.projection(),
      scale: 10
    });
    var ndwi_fine = ndwi.reproject({
      crs: ndwi.projection(),
      scale: 10
    });

    // Define kernel for local statistics
    var kernel = ee.Kernel.square({
      radius: 90,
      units: 'meters',
      normalize: true
    });

    // Calculate LST statistics
    var lst_mean = lst_coarse.reduceNeighborhood({
      reducer: ee.Reducer.mean(),
      kernel: kernel
    });

    var lst_stddev = lst_coarse.reduceNeighborhood({
      reducer: ee.Reducer.stdDev(),
      kernel: kernel
    });

    // Apply empirical weights to indices
    var combined_index = ndvi_fine.multiply(weights.ndvi)
      .add(ndbi_fine.multiply(weights.ndbi))
      .add(ndwi_fine.multiply(weights.ndwi))
      .rename('combined_index');

    // Calculate statistics for combined index
    var combined_mean = combined_index.reduceNeighborhood({
      reducer: ee.Reducer.mean(),
      kernel: kernel
    });

    var combined_stddev = combined_index.reduceNeighborhood({
      reducer: ee.Reducer.stdDev(),
      kernel: kernel
    });

    // Calculate scaling factor
    var scaling_factor = lst_stddev.divide(combined_stddev);

    // Calculate final downscaled LST
    var downscaled_lst = combined_index.multiply(scaling_factor)
      .add(lst_mean.subtract(combined_mean.multiply(scaling_factor)))
      .rename('LST_TLC');

    return downscaled_lst;
  };

  // Apply TLC downscaling with empirical weights
  var lst_tlc = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, weights);

  // Also apply single-index downscaling for comparison
  var weights_ndvi_only = {ndvi: ee.Number(1), ndbi: ee.Number(0), ndwi: ee.Number(0)};
  var weights_ndbi_only = {ndvi: ee.Number(0), ndbi: ee.Number(1), ndwi: ee.Number(0)};
  var weights_ndwi_only = {ndvi: ee.Number(0), ndbi: ee.Number(0), ndwi: ee.Number(1)};

  var lst_ndvi_only = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, weights_ndvi_only);
  var lst_ndbi_only = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, weights_ndbi_only);
  var lst_ndwi_only = tlcDownscale(lstComposite, ndviComposite, ndbiComposite, ndwiComposite, weights_ndwi_only);

  // Validation function
  var validateDownscaling = function(original, downscaled, region, name) {
    // Resample downscaled to original resolution for comparison
    var downscaled_resampled = downscaled.reproject({
      crs: original.projection(),
      scale: 100
    });

    var diff = downscaled_resampled.subtract(original);

    // Calculate RMSE
    var rmse = diff.pow(2).reduceRegion({
      reducer: ee.Reducer.mean(),
      geometry: region,
      scale: 100,
      maxPixels: 1e13
    }).get('LST_TLC');

    rmse = ee.Number(rmse).sqrt();

    // Calculate correlation between original and downscaled
    var combined = ee.Image.cat([original, downscaled_resampled]);
    var corr = combined.reduceRegion({
      reducer: ee.Reducer.pearsonsCorrelation(),
      geometry: region,
      scale: 100,
      maxPixels: 1e13
    }).get('correlation');

    // Calculate R-squared
    var originalMean = original.reduceRegion({
      reducer: ee.Reducer.mean(),
      geometry: region,
      scale: 100,
      maxPixels: 1e13
    }).get('LST');

    var totalSumSquares = original.subtract(ee.Image.constant(originalMean))
      .pow(2)
      .reduceRegion({
        reducer: ee.Reducer.sum(),
        geometry: region,
        scale: 100,
        maxPixels: 1e13
      }).get('LST');

    var residualSumSquares = diff.pow(2).reduceRegion({
      reducer: ee.Reducer.sum(),
      geometry: region,
      scale: 100,
      maxPixels: 1e13
    }).get('LST_TLC');

    var rSquared = ee.Number(1).subtract(ee.Number(residualSumSquares).divide(ee.Number(totalSumSquares)));

    print(name + ' Validation Metrics:');
    print('RMSE:', rmse);
    print('Correlation:', corr);
    print('R-squared:', rSquared);

    return {
      'Method': name,
      'RMSE': rmse,
      'Correlation': corr,
      'R_squared': rSquared
    };
  };

  // Validate all methods
  var validation_weighted = validateDownscaling(lstComposite, lst_tlc, cluster, 'Weighted Multi-Index');
  var validation_ndvi = validateDownscaling(lstComposite, lst_ndvi_only, cluster, 'NDVI Only');
  var validation_ndbi = validateDownscaling(lstComposite, lst_ndbi_only, cluster, 'NDBI Only');
  var validation_ndwi = validateDownscaling(lstComposite, lst_ndwi_only, cluster, 'NDWI Only');

  // Visualization parameters
  var lstViz = {
    min: 20,
    max: 45,
    palette: ['blue', 'green', 'yellow', 'red']
  };

  // Add layers to map
  Map.addLayer(lstComposite.clip(cluster), lstViz, clusterName + ' Original LST (100m)', false);
  Map.addLayer(lst_tlc.clip(cluster), lstViz, clusterName + ' Downscaled LST (10m)', false);

  // Return results for this cluster
  return {
    'cluster_name': clusterName,
    'ndvi_correlation': ndvi_corr,
    'ndbi_correlation': ndbi_corr,
    'ndwi_correlation': ndwi_corr,
    'ndvi_weight': ndvi_corr.abs().divide(total_abs),
    'ndbi_weight': ndbi_corr.abs().divide(total_abs),
    'ndwi_weight': ndwi_corr.abs().divide(total_abs),
    'validation_weighted': validation_weighted,
    'validation_ndvi': validation_ndvi,
    'validation_ndbi': validation_ndbi,
    'validation_ndwi': validation_ndwi,
    'lst_original': lstComposite,
    'lst_downscaled': lst_tlc
  };
};

// Analyze each cluster
var cluster1_results = analyzeCluster(cluster1, 'City Center');
var cluster2_results = analyzeCluster(cluster2, 'Mixed Urban-Green');
var cluster3_results = analyzeCluster(cluster3, 'Residential');
var cluster4_results = analyzeCluster(cluster4, 'Urban-Rural Transition');

// Display clusters on map
Map.centerObject(berlin, 10);
Map.addLayer(clusters, {color: 'red'}, 'Analysis Clusters');

// Add a legend
// Create the panel for the legend
var legend = ui.Panel({
  style: {
    position: 'bottom-left',
    padding: '8px 15px'
  }
});

// Create a title
var legendTitle = ui.Label({
  value: 'LST Legend (°C)',
  style: {
    fontWeight: 'bold',
    fontSize: '16px',
    margin: '0 0 4px 0',
    padding: '0'
  }
});

// Add the title to the panel
legend.add(legendTitle);

// Create and style 1 row of the legend
var makeRow = function(color, name) {
  var colorBox = ui.Label({
    style: {
      backgroundColor: color,
      padding: '8px',
      margin: '0 0 4px 0'
    }
  });

  var description = ui.Label({
    value: name,
    style: {margin: '0 0 4px 6px'}
  });

  return ui.Panel({
    widgets: [colorBox, description],
    layout: ui.Panel.Layout.Flow('horizontal')
  });
};

// Add color and temperature ranges
legend.add(makeRow('blue', '20-25°C'));
legend.add(makeRow('cyan', '25-30°C'));
legend.add(makeRow('green', '30-35°C'));
legend.add(makeRow('yellow', '35-40°C'));
legend.add(makeRow('red', '40-45°C'));

// Add legend to map
Map.add(legend);

// Export results to Drive
Export.table.toDrive({
  collection: ee.FeatureCollection([
    ee.Feature(null, {
      'Cluster': 'City Center',
      'NDVI_Correlation': cluster1_results.ndvi_correlation,
      'NDBI_Correlation': cluster1_results.ndbi_correlation,
      'NDWI_Correlation': cluster1_results.ndwi_correlation,
      'NDVI_Weight': cluster1_results.ndvi_weight,
      'NDBI_Weight': cluster1_results.ndbi_weight,
      'NDWI_Weight': cluster1_results.ndwi_weight,
      'Weighted_RMSE': cluster1_results.validation_weighted.RMSE,
      'Weighted_R_squared': cluster1_results.validation_weighted.R_squared,
      'Weighted_Correlation': cluster1_results.validation_weighted.Correlation,
      'NDVI_Only_RMSE': cluster1_results.validation_ndvi.RMSE,
      'NDBI_Only_RMSE': cluster1_results.validation_ndbi.RMSE,
      'NDWI_Only_RMSE': cluster1_results.validation_ndwi.RMSE
    }),
    ee.Feature(null, {
      'Cluster': 'Mixed Urban-Green',
      'NDVI_Correlation': cluster2_results.ndvi_correlation,
      'NDBI_Correlation': cluster2_results.ndbi_correlation,
      'NDWI_Correlation': cluster2_results.ndwi_correlation,
      'NDVI_Weight': cluster2_results.ndvi_weight,
      'NDBI_Weight': cluster2_results.ndbi_weight,
      'NDWI_Weight': cluster2_results.ndwi_weight,
      'Weighted_RMSE': cluster2_results.validation_weighted.RMSE,
      'Weighted_R_squared': cluster2_results.validation_weighted.R_squared,
      'Weighted_Correlation': cluster2_results.validation_weighted.Correlation,
      'NDVI_Only_RMSE': cluster2_results.validation_ndvi.RMSE,
      'NDBI_Only_RMSE': cluster2_results.validation_ndbi.RMSE,
      'NDWI_Only_RMSE': cluster2_results.validation_ndwi.RMSE
    }),
    ee.Feature(null, {
      'Cluster': 'Residential',
      'NDVI_Correlation': cluster3_results.ndvi_correlation,
      'NDBI_Correlation': cluster3_results.ndbi_correlation,
      'NDWI_Correlation': cluster3_results.ndwi_correlation,
      'NDVI_Weight': cluster3_results.ndvi_weight,
      'NDBI_Weight': cluster3_results.ndbi_weight,
      'NDWI_Weight': cluster3_results.ndwi_weight,
      'Weighted_RMSE': cluster3_results.validation_weighted.RMSE,
      'Weighted_R_squared': cluster3_results.validation_weighted.R_squared,
      'Weighted_Correlation': cluster3_results.validation_weighted.Correlation,
      'NDVI_Only_RMSE': cluster3_results.validation_ndvi.RMSE,
      'NDBI_Only_RMSE': cluster3_results.validation_ndbi.RMSE,
      'NDWI_Only_RMSE': cluster3_results.validation_ndwi.RMSE
    }),
    ee.Feature(null, {
      'Cluster': 'Urban-Rural Transition',
      'NDVI_Correlation': cluster4_results.ndvi_correlation,
      'NDBI_Correlation': cluster4_results.ndbi_correlation,
      'NDWI_Correlation': cluster4_results.ndwi_correlation,
      'NDVI_Weight': cluster4_results.ndvi_weight,
      'NDBI_Weight': cluster4_results.ndbi_weight,
      'NDWI_Weight': cluster4_results.ndwi_weight,
      'Weighted_RMSE': cluster4_results.validation_weighted.RMSE,
      'Weighted_R_squared': cluster4_results.validation_weighted.R_squared,
      'Weighted_Correlation': cluster4_results.validation_weighted.Correlation,
      'NDVI_Only_RMSE': cluster4_results.validation_ndvi.RMSE,
      'NDBI_Only_RMSE': cluster4_results.validation_ndbi.RMSE,
      'NDWI_Only_RMSE': cluster4_results.validation_ndwi.RMSE
    })
  ]),
  description: 'LST_Downscaling_Cluster_Results',
  fileFormat: 'CSV'
});

// Export downscaled LST images for each cluster
Export.image.toDrive({
  image: cluster1_results.lst_downscaled,
  description: 'LST_Downscaled_City_Center',
  scale: 10,
  region: cluster1,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: cluster2_results.lst_downscaled,
  description: 'LST_Downscaled_Mixed_Urban_Green',
  scale: 10,
  region: cluster2,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: cluster3_results.lst_downscaled,
  description: 'LST_Downscaled_Residential',
  scale: 10,
  region: cluster3,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: cluster4_results.lst_downscaled,
  description: 'LST_Downscaled_Urban_Rural_Transition',
  scale: 10,
  region: cluster4,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});
