# Thermal Downscaling of Land Surface Temperature Using Weighted Spectral Indices Integration

## 3. Methodology

### 3.1 Data Acquisition and Selection

Google Earth Engine was utilized to efficiently browse and retrieve Landsat 8 Collection 2 Tier 1 images. This approach saved considerable time by processing datasets in the cloud rather than downloading and processing them locally (<PERSON> & <PERSON>, 2018).

A critical challenge in thermal downscaling studies is the requirement for temporal coincidence between thermal and optical imagery. For this study, both Landsat 8 and Sentinel-2 scenes had to correspond temporally and spatially. Due to Berlin's climate characteristics, the presence of clouds made this requirement challenging to fulfill for large surface areas. As noted by <PERSON> et al. (2021), thermal sensors can only measure land surface temperatures under cloudless conditions; in the presence of clouds, the actual temperature would be measured from the cloud-top thermal signal instead of the land surface.

To address this challenge, the Google Earth Engine code was designed to filter images in the cloud service, validating only data containing cloud cover for the study area of less than 10%. This filtering approach dramatically increased the probability of finding usable images from both satellites. Since the chances of finding completely cloud-free imagery for the entire city of Berlin were minimal, the decision was made to analyze specific parts of the city with cloudless conditions.

After reducing the analyzed data, areas containing diverse land cover types were selected, including parks, streets, buildings, complex areas with buildings, and areas with low surface vegetation such as bare soil. This selection ensured that both pervious and impervious surfaces were adequately represented in the analysis.

### 3.2 Data Sources

#### 3.2.1 Landsat 8 Collection 2 Level 2 (L8 C2 L2)

Landsat 8 Collection 2 Level 2 Surface Temperature data was used as the source of thermal information. The dataset specifications include:
- Spatial resolution: 100m (resampled from native 100m)
- Temporal resolution: 16 days
- Acquisition period: August 9-13, 2022
- Cloud cover threshold: <10%
- Processing level: Surface reflectance and surface temperature
- Key bands: ST_B10 (Surface Temperature)

#### 3.2.2 Sentinel-2 MSI Level 1C

Sentinel-2 data was used to derive high-resolution spectral indices:
- Spatial resolution: 10m (bands 2, 3, 4, 8), 20m (bands 11, 12)
- Temporal resolution: 5 days (combined Sentinel-2A and 2B)
- Acquisition period: August 9-13, 2022
- Cloud cover threshold: <10%
- Key bands:
  - B2 (Blue, 490nm): 10m
  - B3 (Green, 560nm): 10m
  - B4 (Red, 665nm): 10m
  - B8 (NIR, 842nm): 10m
  - B11 (SWIR, 1610nm): 20m

### 3.3 Data Preprocessing

#### 3.3.1 Cloud Masking

Cloud masking was performed on both Landsat 8 and Sentinel-2 imagery to ensure the quality of input data:

For Sentinel-2, the QA60 band was used to identify and mask clouds and cirrus:
```javascript
var maskS2clouds = function(image) {
  var qa = image.select('QA60');
  var cloudBitMask = 1 << 10;
  var cirrusBitMask = 1 << 11;
  var mask = qa.bitwiseAnd(cloudBitMask).eq(0)
      .and(qa.bitwiseAnd(cirrusBitMask).eq(0));
  return image.updateMask(mask);
};
```

For Landsat 8, the QA_PIXEL band was used to identify and mask clouds:
```javascript
var cloudMask = qa.bitwiseAnd(1 << 3).eq(0)
                  .and(qa.bitwiseAnd(1 << 4).eq(0));
```

#### 3.3.2 LST Conversion

Landsat 8 thermal data (ST_B10) was converted from raw digital numbers to degrees Celsius:
```javascript
var thermal = image.select('ST_B10')
                   .multiply(0.00341802)
                   .add(149.0)
                   .subtract(273.15)
                   .rename('LST');
```

#### 3.3.3 Spectral Indices Calculation

Since Sentinel-2 bands operate at 10 and 20 meters resolution, most of the computing process for the indices was done using this satellite. Recognizing that the NDVI index alone is insufficient to identify and explain all thermal patterns in an urban area (Chen et al., 2006), NDBI and NDWI were also incorporated into the analysis. The Sentinel-2 Surface Reflectance dataset was used to derive these high-resolution (10m) vegetation, built-up, and water indices.

Three key spectral indices were calculated from Sentinel-2 data:

1. **NDVI (Normalized Difference Vegetation Index)**:
   ```javascript
   var ndvi = image.normalizedDifference(['B8', 'B4']).rename('NDVI');
   ```
   NDVI = (NIR - Red) / (NIR + Red)

2. **NDBI (Normalized Difference Built-up Index)**:
   ```javascript
   var ndbi = image.normalizedDifference(['B11', 'B8']).rename('NDBI');
   ```
   NDBI = (SWIR - NIR) / (SWIR + NIR)

3. **NDWI (Normalized Difference Water Index)**:
   ```javascript
   var ndwi = image.normalizedDifference(['B3', 'B8']).rename('NDWI');
   ```
   NDWI = (Green - NIR) / (Green + NIR)

#### 3.3.4 Composite Creation

To minimize the impact of temporal variations and data gaps, median composites were created for both LST and spectral indices over the study period:
```javascript
var ndviComposite = s2_processed.select('NDVI').median().clip(aoi);
var ndbiComposite = s2_processed.select('NDBI').median().clip(aoi);
var ndwiComposite = s2_processed.select('NDWI').median().clip(aoi);
var lstComposite = l8_processed.select('LST').median().clip(aoi);
```

### 3.4 Statistical Analysis and Weight Derivation

#### 3.4.1 Correlation Analysis

After calculating the three indices (NDVI, NDBI, NDWI), empirical relationships were established through correlation analysis. For each index, a random forest classification was performed to better understand its relationship with land surface temperature patterns (Breiman, 2001).

Pearson correlation coefficients were calculated between LST and each spectral index to quantify their relationships:
```javascript
var calculateStats = function(lst, index, name) {
  // Resample index to LST resolution for proper comparison
  var index_resampled = index.reproject({
    crs: lst.projection(),
    scale: 100
  });
  
  // Combine LST and index
  var combined = ee.Image.cat([lst, index_resampled]);
  
  // Calculate correlation
  var stats = combined.reduceRegion({
    reducer: ee.Reducer.pearsonsCorrelation(),
    geometry: aoi,
    scale: 100,
    maxPixels: 1e13
  });
  
  return ee.Dictionary({
    'Index': name,
    'Correlation': stats.get('correlation'),
    'LST_mean': lst_stats.get('LST_mean'),
    'LST_stdDev': lst_stats.get('LST_stdDev'),
    'Index_mean': index_stats.get(name + '_mean'),
    'Index_stdDev': index_stats.get(name + '_stdDev')
  });
};
```

#### 3.4.2 Derivation of Empirical Weights

Empirical weights for each index were derived based on their correlation with LST:
```javascript
var ndvi_corr = ee.Number(ndvi_stats.get('Correlation'));
var ndbi_corr = ee.Number(ndbi_stats.get('Correlation'));
var ndwi_corr = ee.Number(ndwi_stats.get('Correlation'));

var total_abs = ndvi_corr.abs()
  .add(ndbi_corr.abs())
  .add(ndwi_corr.abs());

var weights = {
  ndvi: ndvi_corr.divide(total_abs),
  ndbi: ndbi_corr.divide(total_abs),
  ndwi: ndwi_corr.divide(total_abs)
};
```

The weight for each index was calculated as the absolute value of its correlation coefficient divided by the sum of the absolute values of all correlation coefficients. This approach ensures that:
1. The weights sum to 1
2. Indices with stronger correlation (positive or negative) have greater influence
3. The sign of the correlation is preserved, allowing for proper directional influence

### 3.5 TLC Downscaling Implementation

The Temperature-Land Cover (TLC) downscaling algorithm was implemented with the following steps:

#### 3.5.1 Algorithm Formulation

The TLC approach is based on the assumption that LST variations at fine resolution can be predicted from the relationship between LST and land cover characteristics at coarse resolution. The algorithm can be expressed as:

LST_fine = LST_coarse_mean + (LST_coarse_stddev / Index_coarse_stddev) × (Index_fine - Index_coarse_mean)

Where:
- LST_fine is the downscaled LST at fine resolution
- LST_coarse_mean is the mean LST within a local window
- LST_coarse_stddev is the standard deviation of LST within a local window
- Index_coarse_stddev is the standard deviation of the index within a local window
- Index_fine is the index value at fine resolution
- Index_coarse_mean is the mean index value within a local window

#### 3.5.2 Multi-Index Implementation

Our implementation extends the basic TLC approach by incorporating multiple indices with empirical weights:
```javascript
var tlcDownscale = function(lst, ndvi, ndbi, ndwi, weights) {
  // Resample LST to intermediate resolution (90m)
  var lst_coarse = lst.reproject({
    crs: lst.projection(),
    scale: 90
  });
  
  // Keep indices at 10m resolution
  var ndvi_fine = ndvi.reproject({
    crs: ndvi.projection(),
    scale: 10
  });
  var ndbi_fine = ndbi.reproject({
    crs: ndbi.projection(),
    scale: 10
  });
  var ndwi_fine = ndwi.reproject({
    crs: ndwi.projection(),
    scale: 10
  });
  
  // Define kernel for local statistics
  var kernel = ee.Kernel.square({
    radius: 90,
    units: 'meters',
    normalize: true
  });
  
  // Calculate LST statistics
  var lst_mean = lst_coarse.reduceNeighborhood({
    reducer: ee.Reducer.mean(),
    kernel: kernel
  });
  
  var lst_stddev = lst_coarse.reduceNeighborhood({
    reducer: ee.Reducer.stdDev(),
    kernel: kernel
  });

  // Apply empirical weights to indices
  var combined_index = ndvi_fine.multiply(weights.ndvi)
    .add(ndbi_fine.multiply(weights.ndbi))
    .add(ndwi_fine.multiply(weights.ndwi))
    .rename('combined_index');
    
  // Calculate statistics for combined index
  var combined_mean = combined_index.reduceNeighborhood({
    reducer: ee.Reducer.mean(),
    kernel: kernel
  });
  
  var combined_stddev = combined_index.reduceNeighborhood({
    reducer: ee.Reducer.stdDev(),
    kernel: kernel
  });
  
  // Calculate scaling factor
  var scaling_factor = lst_stddev.divide(combined_stddev);
  
  // Calculate final downscaled LST
  var downscaled_lst = combined_index.multiply(scaling_factor)
    .add(lst_mean.subtract(combined_mean.multiply(scaling_factor)))
    .rename('LST_TLC');
    
  return downscaled_lst;
};
```

Key innovations in our implementation include:
1. **Weighted combination of multiple indices**: Instead of using a single index, we combine NDVI, NDBI, and NDWI with weights derived from their correlation with LST.
2. **Adaptive kernel size**: A square kernel with a radius of 90 meters is used to calculate local statistics, balancing local detail and statistical robustness.
3. **Intermediate resampling**: LST is resampled to an intermediate resolution (90m) before downscaling to 10m, reducing artifacts and preserving thermal patterns.

### 3.6 Validation Methodology

The downscaled LST product was validated against the original LST data to assess the accuracy of the downscaling process:
```javascript
var validateDownscaling = function(original, downscaled, region) {
  // Resample downscaled to original resolution for comparison
  var downscaled_resampled = downscaled.reproject({
    crs: original.projection(),
    scale: 100
  });
  
  var diff = downscaled_resampled.subtract(original);

  var stats = diff.pow(2).reduceRegion({
    reducer: ee.Reducer.mean(),
    geometry: region,
    scale: 100,
    maxPixels: 1e13
  }).map(function(key, value) {
    return ee.Number(value).sqrt();
  });
  
  return ee.Dictionary({
    'Mean_Difference': stats.get('mean'),
    'StdDev_Difference': stats.get('stdDev'),
    'RMSE': stats.get('rmseMean')
  });
};
```

The validation approach includes:
1. **Resampling**: The downscaled LST (10m) is resampled back to the original resolution (100m) for direct comparison.
2. **Difference calculation**: The difference between the resampled downscaled LST and the original LST is calculated.
3. **Statistical metrics**: Several metrics are computed to quantify the agreement between the original and downscaled data:
   - Mean Difference: Average difference between original and downscaled LST
   - Standard Deviation of Difference: Variability of differences
   - Root Mean Square Error (RMSE): Square root of the average squared differences

### 3.7 Comparative Analysis

To evaluate the effectiveness of the multi-index weighted approach, a comparative analysis was conducted against traditional single-index TLC downscaling methods. The same downscaling algorithm was applied using:
1. NDVI only
2. NDBI only
3. NDWI only
4. The proposed weighted combination of all three indices

For each approach, the same validation metrics were calculated to enable direct comparison of performance.

## References

Breiman, L. (2001). Random forests. Machine Learning, 45(1), 5-32.

Chen, X. L., Zhao, H. M., Li, P. X., & Yin, Z. Y. (2006). Remote sensing image-based analysis of the relationship between urban heat island and land use/cover changes. Remote Sensing of Environment, 104(2), 133-146.

Kumar, L., & Mutanga, O. (2018). Google Earth Engine applications since inception: Usage, trends, and potential. Remote Sensing, 10(10), 1509.

Mo, Y., Kang, S., Hwang, W. H., Li, F., Yan, X., & Ren, X. (2021). Spatiotemporal variations in land surface temperature and its relationship with climate factors and land cover over the Tibetan Plateau. International Journal of Climatology, 41(S1), E3425-E3444.
