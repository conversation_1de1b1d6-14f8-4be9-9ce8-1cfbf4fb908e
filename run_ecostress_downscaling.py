"""
Run ECOSTRESS Downscaling JavaScript code through the Earth Engine Python API.
This script reads the JavaScript file and executes it in Earth Engine.
"""

import ee
import os
import time
import webbrowser

# Initialize Earth Engine
ee.Initialize()

# Path to the JavaScript file
js_file_path = 'ECOSTRESS_Downscaling.js'

# Read the JavaScript code
with open(js_file_path, 'r') as file:
    js_code = file.read()

# Function to monitor and retrieve task status
def check_task_status(task):
    """Check the status of an Earth Engine task."""
    status = task.status()
    state = status['state']
    print(f"Task: {task.config['description']} - Status: {state}")
    return state

# Execute the JavaScript code
print(f"Executing {js_file_path}...")

# Create a temporary script that includes visualization and analysis
# but excludes the export tasks which we'll handle separately
analysis_code = js_code.split("// Export results to Drive")[0]

# Execute the analysis portion
result = ee.Execute(analysis_code)

# Get the map URL to visualize results
# We need to create a map with the layers we want to display
map_code = """
// Create a map centered on Berlin
var map = ui.Map();
map.setCenter(13.4, 52.52, 10);

// Add the clusters
map.addLayer(clusters, {color: 'red'}, 'Analysis Clusters');

// Add the ECOSTRESS layers for Urban Core (Kreuzberg)
var lstViz = {min: 20, max: 45, palette: ['#0000FF', '#00FFFF', '#00FF00', '#FFFF00', '#FF0000']};
map.addLayer(eco_cluster1_results.lst_original.clip(cluster1), lstViz, 'Urban Core ECOSTRESS Original (70m)');
map.addLayer(eco_cluster1_results.lst_tlc.clip(cluster1), lstViz, 'Urban Core ECOSTRESS Downscaled (10m)');

// Return the map for display
return map;
"""

# Execute the map code and get the URL
try:
    map_result = ee.Execute(map_code)
    map_url = ee.data.getValue(map_result)['mapUrl']
    print(f"Map URL: {map_url}")
    
    # Open the map in a browser
    webbrowser.open(map_url)
except Exception as e:
    print(f"Error creating map: {e}")
    # Fallback to a simple map centered on Berlin
    berlin = ee.Geometry.Point([13.4, 52.52])
    map_url = ee.Image().visualize().getThumbURL({
        'region': berlin.buffer(20000).bounds(),
        'dimensions': '1000',
        'format': 'png'
    })
    print(f"Fallback map URL: {map_url}")
    webbrowser.open(map_url)

# Now handle the export tasks
print("\nSetting up export tasks...")

# Extract the export code sections
export_code = js_code.split("// Export results to Drive")[1]

# Function to create and start an export task
def start_export_task(export_type, description, config):
    """Create and start an Earth Engine export task."""
    if export_type == 'table':
        task = ee.batch.Export.table.toDrive(**config)
    elif export_type == 'image':
        task = ee.batch.Export.image.toDrive(**config)
    else:
        raise ValueError(f"Unknown export type: {export_type}")
    
    task.start()
    print(f"Started {export_type} export task: {description}")
    return task

# Create and start the CSV export task
csv_config = {
    'collection': ee.FeatureCollection([
        ee.Feature(None, {
            'Cluster': 'Urban Core (Kreuzberg)',
            'NDVI_Correlation': ee.Number(0),  # Placeholder values
            'NDBI_Correlation': ee.Number(0),
            'NDWI_Correlation': ee.Number(0),
            'ALBEDO_Correlation': ee.Number(0)
        })
    ]),
    'description': 'ECOSTRESS_Downscaling_Results',
    'fileFormat': 'CSV'
}

# Start the CSV export task
csv_task = start_export_task('table', 'ECOSTRESS_Downscaling_Results', csv_config)

# Create and start image export tasks for the first cluster only (as an example)
# For the original ECOSTRESS image
original_config = {
    'image': ee.Image(0),  # Placeholder image
    'description': 'ECOSTRESS_Original_Urban_Core',
    'scale': 70,
    'region': ee.Geometry.Rectangle([13.41, 52.49, 13.45, 52.53]),
    'maxPixels': 1e13,
    'fileFormat': 'GeoTIFF'
}

# For the downscaled ECOSTRESS image
downscaled_config = {
    'image': ee.Image(0),  # Placeholder image
    'description': 'ECOSTRESS_Downscaled_Urban_Core',
    'scale': 10,
    'region': ee.Geometry.Rectangle([13.41, 52.49, 13.45, 52.53]),
    'maxPixels': 1e13,
    'fileFormat': 'GeoTIFF'
}

# Start the image export tasks
original_task = start_export_task('image', 'ECOSTRESS_Original_Urban_Core', original_config)
downscaled_task = start_export_task('image', 'ECOSTRESS_Downscaled_Urban_Core', downscaled_config)

print("\nTasks have been started. Check the Earth Engine Task tab to monitor progress.")
print("Note: The export tasks use placeholder data. To export actual results, run the JavaScript code directly in the Earth Engine Code Editor.")

# Monitor tasks for a short period
print("\nMonitoring tasks for 60 seconds...")
for _ in range(6):
    csv_status = check_task_status(csv_task)
    original_status = check_task_status(original_task)
    downscaled_status = check_task_status(downscaled_task)
    
    if all(status in ['COMPLETED', 'FAILED', 'CANCELLED'] for status in [csv_status, original_status, downscaled_status]):
        print("All tasks have completed or failed.")
        break
    
    time.sleep(10)

print("\nScript execution complete. For full results, run the JavaScript code in the Earth Engine Code Editor.")
