  // Validate all methods
  var validation_weighted = validateDownscaling(lstComposite, lst_tlc, cluster, 'Weighted Multi-Index');
  var validation_ndvi = validateDownscaling(lstComposite, lst_ndvi_only, cluster, 'NDVI Only');
  var validation_ndbi = validateDownscaling(lstComposite, lst_ndbi_only, cluster, 'NDBI Only');
  var validation_ndwi = validateDownscaling(lstComposite, lst_ndwi_only, cluster, 'NDWI Only');
  var validation_albedo = validateDownscaling(lstComposite, lst_albedo_only, cluster, 'ALBEDO Only');

  // Visualization parameters
  var lstViz = {
    min: 20,
    max: 45,
    palette: ['blue', 'green', 'yellow', 'red']
  };

  // Add layers to map
  Map.addLayer(lstComposite.clip(cluster), lstViz, clusterName + ' Original LST (100m)', false);
  Map.addLayer(lst_tlc.clip(cluster), lstViz, clusterName + ' Downscaled LST (10m)', false);

  // Return results for this cluster
  return {
    'cluster_name': clusterName,
    'ndvi_correlation': ndvi_corr,
    'ndbi_correlation': ndbi_corr,
    'ndwi_correlation': ndwi_corr,
    'albedo_correlation': albedo_corr,
    'ndvi_weight': ndvi_corr.abs().divide(total_abs),
    'ndbi_weight': ndbi_corr.abs().divide(total_abs),
    'ndwi_weight': ndwi_corr.abs().divide(total_abs),
    'albedo_weight': albedo_corr.abs().divide(total_abs),
    'validation_weighted': validation_weighted,
    'validation_ndvi': validation_ndvi,
    'validation_ndbi': validation_ndbi,
    'validation_ndwi': validation_ndwi,
    'validation_albedo': validation_albedo,
    'lst_original': lstComposite,
    'lst_downscaled': lst_tlc
  };
};

// Analyze each cluster
var cluster1_results = analyzeCluster(cluster1, 'City Center (Mitte)');
var cluster2_results = analyzeCluster(cluster2, 'Mixed Urban (Prenzlauer Berg)');
var cluster3_results = analyzeCluster(cluster3, 'Southwest Residential (Wilmersdorf)');
var cluster4_results = analyzeCluster(cluster4, 'Urban-Rural Transition (Köpenick)');

// Display clusters on map
Map.centerObject(berlin, 10);
Map.addLayer(clusters, {color: 'red'}, 'Analysis Clusters');

// Add a legend
// Create the panel for the legend
var legend = ui.Panel({
  style: {
    position: 'bottom-left',
    padding: '8px 15px'
  }
});

// Create a title
var legendTitle = ui.Label({
  value: 'LST Legend (°C)',
  style: {
    fontWeight: 'bold',
    fontSize: '16px',
    margin: '0 0 4px 0',
    padding: '0'
  }
});

// Add the title to the panel
legend.add(legendTitle);

// Create and style 1 row of the legend
var makeRow = function(color, name) {
  var colorBox = ui.Label({
    style: {
      backgroundColor: color,
      padding: '8px',
      margin: '0 0 4px 0'
    }
  });

  var description = ui.Label({
    value: name,
    style: {margin: '0 0 4px 6px'}
  });

  return ui.Panel({
    widgets: [colorBox, description],
    layout: ui.Panel.Layout.Flow('horizontal')
  });
};

// Add color and temperature ranges
legend.add(makeRow('blue', '20-25°C'));
legend.add(makeRow('cyan', '25-30°C'));
legend.add(makeRow('green', '30-35°C'));
legend.add(makeRow('yellow', '35-40°C'));
legend.add(makeRow('red', '40-45°C'));

// Add legend to map
Map.add(legend);

// Export results to Drive
Export.table.toDrive({
  collection: ee.FeatureCollection([
    ee.Feature(null, {
      'Cluster': 'City Center (Mitte)',
      'NDVI_Correlation': cluster1_results.ndvi_correlation,
      'NDBI_Correlation': cluster1_results.ndbi_correlation,
      'NDWI_Correlation': cluster1_results.ndwi_correlation,
      'ALBEDO_Correlation': cluster1_results.albedo_correlation,
      'NDVI_Weight': cluster1_results.ndvi_weight,
      'NDBI_Weight': cluster1_results.ndbi_weight,
      'NDWI_Weight': cluster1_results.ndwi_weight,
      'ALBEDO_Weight': cluster1_results.albedo_weight,
      'Weighted_RMSE': cluster1_results.validation_weighted.RMSE,
      'Weighted_R_squared': cluster1_results.validation_weighted.R_squared,
      'Weighted_Correlation': cluster1_results.validation_weighted.Correlation,
      'NDVI_Only_RMSE': cluster1_results.validation_ndvi.RMSE,
      'NDBI_Only_RMSE': cluster1_results.validation_ndbi.RMSE,
      'NDWI_Only_RMSE': cluster1_results.validation_ndwi.RMSE,
      'ALBEDO_Only_RMSE': cluster1_results.validation_albedo.RMSE
    }),
    ee.Feature(null, {
      'Cluster': 'Mixed Urban (Prenzlauer Berg)',
      'NDVI_Correlation': cluster2_results.ndvi_correlation,
      'NDBI_Correlation': cluster2_results.ndbi_correlation,
      'NDWI_Correlation': cluster2_results.ndwi_correlation,
      'ALBEDO_Correlation': cluster2_results.albedo_correlation,
      'NDVI_Weight': cluster2_results.ndvi_weight,
      'NDBI_Weight': cluster2_results.ndbi_weight,
      'NDWI_Weight': cluster2_results.ndwi_weight,
      'ALBEDO_Weight': cluster2_results.albedo_weight,
      'Weighted_RMSE': cluster2_results.validation_weighted.RMSE,
      'Weighted_R_squared': cluster2_results.validation_weighted.R_squared,
      'Weighted_Correlation': cluster2_results.validation_weighted.Correlation,
      'NDVI_Only_RMSE': cluster2_results.validation_ndvi.RMSE,
      'NDBI_Only_RMSE': cluster2_results.validation_ndbi.RMSE,
      'NDWI_Only_RMSE': cluster2_results.validation_ndwi.RMSE,
      'ALBEDO_Only_RMSE': cluster2_results.validation_albedo.RMSE
    }),
    ee.Feature(null, {
      'Cluster': 'Southwest Residential (Wilmersdorf)',
      'NDVI_Correlation': cluster3_results.ndvi_correlation,
      'NDBI_Correlation': cluster3_results.ndbi_correlation,
      'NDWI_Correlation': cluster3_results.ndwi_correlation,
      'ALBEDO_Correlation': cluster3_results.albedo_correlation,
      'NDVI_Weight': cluster3_results.ndvi_weight,
      'NDBI_Weight': cluster3_results.ndbi_weight,
      'NDWI_Weight': cluster3_results.ndwi_weight,
      'ALBEDO_Weight': cluster3_results.albedo_weight,
      'Weighted_RMSE': cluster3_results.validation_weighted.RMSE,
      'Weighted_R_squared': cluster3_results.validation_weighted.R_squared,
      'Weighted_Correlation': cluster3_results.validation_weighted.Correlation,
      'NDVI_Only_RMSE': cluster3_results.validation_ndvi.RMSE,
      'NDBI_Only_RMSE': cluster3_results.validation_ndbi.RMSE,
      'NDWI_Only_RMSE': cluster3_results.validation_ndwi.RMSE,
      'ALBEDO_Only_RMSE': cluster3_results.validation_albedo.RMSE
    }),
    ee.Feature(null, {
      'Cluster': 'Urban-Rural Transition (Köpenick)',
      'NDVI_Correlation': cluster4_results.ndvi_correlation,
      'NDBI_Correlation': cluster4_results.ndbi_correlation,
      'NDWI_Correlation': cluster4_results.ndwi_correlation,
      'ALBEDO_Correlation': cluster4_results.albedo_correlation,
      'NDVI_Weight': cluster4_results.ndvi_weight,
      'NDBI_Weight': cluster4_results.ndbi_weight,
      'NDWI_Weight': cluster4_results.ndwi_weight,
      'ALBEDO_Weight': cluster4_results.albedo_weight,
      'Weighted_RMSE': cluster4_results.validation_weighted.RMSE,
      'Weighted_R_squared': cluster4_results.validation_weighted.R_squared,
      'Weighted_Correlation': cluster4_results.validation_weighted.Correlation,
      'NDVI_Only_RMSE': cluster4_results.validation_ndvi.RMSE,
      'NDBI_Only_RMSE': cluster4_results.validation_ndbi.RMSE,
      'NDWI_Only_RMSE': cluster4_results.validation_ndwi.RMSE,
      'ALBEDO_Only_RMSE': cluster4_results.validation_albedo.RMSE
    })
  ]),
  description: 'LST_Downscaling_Albedo_Results',
  fileFormat: 'CSV'
});

// Export downscaled LST images for each cluster
Export.image.toDrive({
  image: cluster1_results.lst_downscaled,
  description: 'LST_Downscaled_City_Center_Albedo',
  scale: 10,
  region: cluster1,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: cluster2_results.lst_downscaled,
  description: 'LST_Downscaled_Mixed_Urban_Albedo',
  scale: 10,
  region: cluster2,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: cluster3_results.lst_downscaled,
  description: 'LST_Downscaled_Southwest_Residential_Albedo',
  scale: 10,
  region: cluster3,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});

Export.image.toDrive({
  image: cluster4_results.lst_downscaled,
  description: 'LST_Downscaled_Urban_Rural_Transition_Albedo',
  scale: 10,
  region: cluster4,
  maxPixels: 1e13,
  fileFormat: 'GeoTIFF'
});
