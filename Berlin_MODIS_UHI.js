// Berlin MODIS LST and UHI Analysis (2013 and 2024)
// Focusing on 10:00 AM timeframe to align with Landsat and Sentinel-2 crossing times

// Define Berlin urban and rural areas
var berlinCenter = ee.Geometry.Point([13.404954, 52.520008]); // Berlin city center
var urbanArea = berlinCenter.buffer(15000); // 15km urban radius (Berlin is larger than Munich)
var ruralRing = ee.Geometry.MultiPolygon([
  berlinCenter.buffer(40000).difference(berlinCenter.buffer(25000))
]);

// Define time periods for analysis (same as in HLS30.js)
var periods = [
  {year: 2013, startDate: '2013-07-01', endDate: '2013-08-31'},
  {year: 2024, startDate: '2024-05-01', endDate: '2024-06-30'}
];

// Function to convert MODIS LST to Celsius
function convertToCelsius(image, bandName) {
  return image
    .select(bandName)
    .multiply(0.02)
    .subtract(273.15);
}

// Function to get MODIS Terra data (morning pass ~10:30 local time)
function getModisLSTandUHI(year, startDate, endDate) {
  // Get MODIS Terra data for daytime (~10:30 AM)
  var modisTerraMorning = ee.ImageCollection('MODIS/061/MOD11A1')
    .filterDate(startDate, endDate)
    .select(['LST_Day_1km']);
  
  // Create a composite (median) for the period
  var morningLST = modisTerraMorning.median();
  
  if (morningLST) {
    // Convert LST to Celsius
    var dayLST = convertToCelsius(morningLST, 'LST_Day_1km')
      .rename('LST_Morning');
    
    // Calculate means for urban and rural areas
    var urbanMorningMean = dayLST.reduceRegion({
      reducer: ee.Reducer.mean(),
      geometry: urbanArea,
      scale: 1000,
      maxPixels: 1e9
    }).get('LST_Morning');
    
    var ruralMorningMean = dayLST.reduceRegion({
      reducer: ee.Reducer.mean(),
      geometry: ruralRing,
      scale: 1000,
      maxPixels: 1e9
    }).get('LST_Morning');
    
    // Calculate UHI
    var morningUHI = dayLST.subtract(ee.Image.constant(ruralMorningMean))
      .rename('UHI_Morning');
    
    return ee.Image.cat([dayLST, morningUHI])
      .set('year', year)
      .set('period', startDate + ' to ' + endDate)
      .set('urban_morning_mean', urbanMorningMean)
      .set('rural_morning_mean', ruralMorningMean)
      .set('morning_uhi_intensity', ee.Number(urbanMorningMean).subtract(ruralMorningMean));
  }
  return null;
}

// Process each period
var results = periods.map(function(period) {
  return getModisLSTandUHI(period.year, period.startDate, period.endDate);
});

// Visualization parameters
var lstVis = {
  min: 20,
  max: 40,
  palette: ['0000FF', '00FFFF', 'FFFF00', 'FF0000', 'DarkRed']
};

var uhiVis = {
  min: 0,
  max: 5,
  palette: ['FFFFFF', 'FFFF00', 'FF9900', 'FF0000', 'DarkRed']
};

// Add layers to map
Map.centerObject(urbanArea, 9);

// Add 2013 layers
Map.addLayer(results[0].select('LST_Morning'), 
  lstVis, 
  '2013 LST (~10:30 AM)', false);
  
Map.addLayer(results[0].select('UHI_Morning'),
  uhiVis,
  '2013 UHI (~10:30 AM)', false);

// Add 2024 layers
Map.addLayer(results[1].select('LST_Morning'), 
  lstVis, 
  '2024 LST (~10:30 AM)', false);
  
Map.addLayer(results[1].select('UHI_Morning'),
  uhiVis,
  '2024 UHI (~10:30 AM)', false);

// Calculate LST and UHI difference between 2024 and 2013
var lstDiff = results[1].select('LST_Morning')
  .subtract(results[0].select('LST_Morning'))
  .rename('LST_Diff');

var uhiDiff = results[1].select('UHI_Morning')
  .subtract(results[0].select('UHI_Morning'))
  .rename('UHI_Diff');

// Add difference layers
Map.addLayer(lstDiff, 
  {min: -3, max: 3, palette: ['0000FF', 'FFFFFF', 'FF0000']}, 
  'LST Change (2024-2013)', false);

Map.addLayer(uhiDiff, 
  {min: -2, max: 2, palette: ['0000FF', 'FFFFFF', 'FF0000']}, 
  'UHI Change (2024-2013)', false);

// Add study area boundaries
Map.addLayer(urbanArea, {color: 'black'}, 'Urban Area', false);
Map.addLayer(ruralRing, {color: 'white'}, 'Rural Reference', false);

// Print statistics
print('Statistics for 2013:',
  'Morning UHI Intensity (~10:30 AM):', results[0].get('morning_uhi_intensity'),
  'Urban Morning Mean (~10:30 AM):', results[0].get('urban_morning_mean'),
  'Rural Morning Mean (~10:30 AM):', results[0].get('rural_morning_mean')
);

print('Statistics for 2024:',
  'Morning UHI Intensity (~10:30 AM):', results[1].get('morning_uhi_intensity'),
  'Urban Morning Mean (~10:30 AM):', results[1].get('urban_morning_mean'),
  'Rural Morning Mean (~10:30 AM):', results[1].get('rural_morning_mean')
);

// Calculate change in UHI intensity
var uhiIntensityChange = ee.Number(results[1].get('morning_uhi_intensity'))
  .subtract(results[0].get('morning_uhi_intensity'));

print('UHI Intensity Change (2024-2013):', uhiIntensityChange);

// Add legend
var legend = ui.Panel({
  style: {
    position: 'bottom-left',
    padding: '8px 15px'
  }
});

var legendTitle = ui.Label({
  value: 'MODIS LST & UHI (°C)\n(~10:30 AM)',
  style: {
    fontWeight: 'bold',
    fontSize: '16px',
    margin: '0 0 4px 0',
    padding: '0'
  }
});

legend.add(legendTitle);

function makeRow(color, name) {
  var colorBox = ui.Label({
    style: {
      backgroundColor: color,
      padding: '8px',
      margin: '0 0 4px 0'
    }
  });
  var description = ui.Label({
    value: name,
    style: {margin: '0 0 4px 6px'}
  });
  return ui.Panel({
    widgets: [colorBox, description],
    layout: ui.Panel.Layout.Flow('horizontal')
  });
}

// Add LST legend items
legend.add(ui.Label('Land Surface Temperature:'));
legend.add(makeRow('#0000FF', '20°C (Cold)'));
legend.add(makeRow('#00FFFF', '25°C'));
legend.add(makeRow('#FFFF00', '30°C'));
legend.add(makeRow('#FF0000', '35°C'));
legend.add(makeRow('#8B0000', '40°C (Hot)'));

// Add UHI legend items
legend.add(ui.Label('Urban Heat Island:'));
legend.add(makeRow('#FFFFFF', '0°C (No UHI)'));
legend.add(makeRow('#FFFF00', '1-2°C (Weak UHI)'));
legend.add(makeRow('#FF9900', '2-3°C (Moderate UHI)'));
legend.add(makeRow('#FF0000', '3-4°C (Strong UHI)'));
legend.add(makeRow('#8B0000', '>4°C (Very Strong UHI)'));

Map.add(legend);

// Export results
Export.image.toDrive({
  image: results[0],
  description: 'Berlin_MODIS_LST_UHI_2013',
  scale: 1000,
  region: urbanArea.bounds(),
  maxPixels: 1e9
});

Export.image.toDrive({
  image: results[1],
  description: 'Berlin_MODIS_LST_UHI_2024',
  scale: 1000,
  region: urbanArea.bounds(),
  maxPixels: 1e9
});

Export.image.toDrive({
  image: ee.Image.cat([lstDiff, uhiDiff]),
  description: 'Berlin_MODIS_LST_UHI_Change_2013_2024',
  scale: 1000,
  region: urbanArea.bounds(),
  maxPixels: 1e9
});

// Add a dropdown for selecting which MODIS layer to view
var modisLayerSelector = ui.Select({
  items: [
    '2013 LST (~10:30 AM)',
    '2024 LST (~10:30 AM)',
    '2013 UHI (~10:30 AM)',
    '2024 UHI (~10:30 AM)',
    'LST Change (2024-2013)',
    'UHI Change (2024-2013)',
    'Urban Area',
    'Rural Reference'
  ],
  value: '2013 LST (~10:30 AM)',
  onChange: function(selected) {
    // Hide all MODIS layers first
    for (var i = 0; i < 8; i++) {
      if (Map.layers().length() > i) {
        Map.layers().get(i).setShown(false);
      }
    }

    // Show the selected layer
    var layerIndex = {
      '2013 LST (~10:30 AM)': 0,
      '2024 LST (~10:30 AM)': 2,
      '2013 UHI (~10:30 AM)': 1,
      '2024 UHI (~10:30 AM)': 3,
      'LST Change (2024-2013)': 4,
      'UHI Change (2024-2013)': 5,
      'Urban Area': 6,
      'Rural Reference': 7
    };
    
    Map.layers().get(layerIndex[selected]).setShown(true);
  }
});

// Add a panel with the MODIS layer selector
Map.add(ui.Panel({
  widgets: [ui.Label('Select MODIS Layer:'), modisLayerSelector],
  style: {position: 'top-left'}
}));
